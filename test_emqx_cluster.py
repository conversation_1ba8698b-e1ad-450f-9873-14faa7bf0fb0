#!/usr/bin/env python3
"""
EMQX Cluster Monitoring Test Script
----------------------------------
This script tests the EMQX cluster monitoring functionality.
It can be used to verify that the cluster monitoring works correctly.

Usage:
    python test_emqx_cluster.py [--simulate-failure]
"""

import os
import sys
import argparse
import subprocess
import re
from dotenv import load_dotenv

# Add the project root to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agent.auto_heal import AutoHealer
from utils.helpers import setup_logging

def run_command(command):
    """Run a shell command and return success status and output."""
    try:
        result = subprocess.run(
            command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            timeout=30
        )
        return result.returncode == 0, result.stdout.strip()
    except subprocess.TimeoutExpired:
        return False, "Command timed out"
    except Exception as e:
        return False, str(e)

def test_cluster_status_parsing():
    """Test the cluster status parsing with sample data."""
    print("=== Testing Cluster Status Parsing ===")
    
    # Sample EMQX cluster status output
    sample_output = """Cluster status: #{running_nodes =>
                          ['<EMAIL>',
                           '<EMAIL>',
                           '<EMAIL>',
                           '<EMAIL>'],
                      stopped_nodes => []}"""
    
    def parse_nodes(section, output):
        m = re.search(rf"{section}\s*=>\s*\[(.*?)\]", output, re.S)
        if not m:
            return []
        content = m.group(1)
        return re.findall(r"'([^']+)'", content)

    running_nodes = parse_nodes('running_nodes', sample_output)
    stopped_nodes = parse_nodes('stopped_nodes', sample_output)
    
    print(f"Sample output parsed successfully:")
    print(f"  Running nodes: {running_nodes}")
    print(f"  Stopped nodes: {stopped_nodes}")
    print()

def test_live_cluster_status():
    """Test getting live cluster status from EMQX."""
    print("=== Testing Live Cluster Status ===")
    
    success, output = run_command("emqx_ctl cluster status")
    if success:
        print("✓ Successfully retrieved cluster status")
        print(f"Output preview: {output[:200]}...")
        
        # Parse the output
        def parse_nodes(section):
            m = re.search(rf"{section}\s*=>\s*\[(.*?)\]", output, re.S)
            if not m:
                return []
            content = m.group(1)
            return re.findall(r"'([^']+)'", content)

        running_nodes = parse_nodes('running_nodes')
        stopped_nodes = parse_nodes('stopped_nodes')
        
        print(f"  Running nodes ({len(running_nodes)}): {running_nodes}")
        print(f"  Stopped nodes ({len(stopped_nodes)}): {stopped_nodes}")
    else:
        print(f"✗ Failed to retrieve cluster status: {output}")
    print()

def test_cluster_monitoring():
    """Test the cluster monitoring functionality."""
    print("=== Testing Cluster Monitoring ===")
    
    # Load environment variables
    load_dotenv()
    
    # Initialize the auto healer
    auto_healer = AutoHealer()
    
    print(f"Cluster monitoring enabled: {auto_healer.cluster_check_enabled}")
    print(f"Expected nodes: {auto_healer.emqx_expected_nodes}")
    print(f"Alert cooldown: {auto_healer.cluster_alert_cooldown} seconds")
    print()
    
    # Run the cluster check
    print("Running cluster check...")
    try:
        result = auto_healer._check_emqx_cluster()
        if result:
            print("✓ Cluster check completed successfully")
            print(f"  Running nodes: {result['running_nodes']}")
            print(f"  Stopped nodes: {result['stopped_nodes']}")
            print(f"  Expected nodes: {result['expected_nodes']}")
            print(f"  Cluster healthy: {result['healthy']}")
            if result['alert_reasons']:
                print(f"  Alert reasons: {result['alert_reasons']}")
        else:
            print("✗ Cluster check returned no data")
    except Exception as e:
        print(f"✗ Error during cluster check: {str(e)}")
    print()

def simulate_failure_scenario():
    """Simulate a failure scenario for testing."""
    print("=== Simulating Failure Scenario ===")
    print("This would simulate a node failure by temporarily modifying")
    print("the expected nodes configuration to include a non-existent node.")
    print()
    
    # Temporarily modify expected nodes to include a fake node
    original_nodes = os.getenv('EMQX_EXPECTED_NODES', '')
    fake_node = '<EMAIL>'
    
    if original_nodes:
        test_nodes = original_nodes + ',' + fake_node
    else:
        test_nodes = fake_node
    
    print(f"Original expected nodes: {original_nodes}")
    print(f"Test expected nodes (with fake node): {test_nodes}")
    
    # Set the environment variable temporarily
    os.environ['EMQX_EXPECTED_NODES'] = test_nodes
    
    try:
        # Test with the modified configuration
        auto_healer = AutoHealer()
        result = auto_healer._check_emqx_cluster()
        
        if result:
            print("Test result:")
            print(f"  Cluster healthy: {result['healthy']}")
            print(f"  Alert reasons: {result['alert_reasons']}")
        else:
            print("No result returned from cluster check")
            
    finally:
        # Restore original configuration
        if original_nodes:
            os.environ['EMQX_EXPECTED_NODES'] = original_nodes
        else:
            os.environ.pop('EMQX_EXPECTED_NODES', None)
    
    print()

def main():
    parser = argparse.ArgumentParser(description='Test EMQX cluster monitoring')
    parser.add_argument('--simulate-failure', action='store_true',
                       help='Simulate a failure scenario for testing')
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(debug=True)
    
    print("EMQX Cluster Monitoring Test")
    print("=" * 40)
    print()
    
    # Run tests
    test_cluster_status_parsing()
    test_live_cluster_status()
    test_cluster_monitoring()
    
    if args.simulate_failure:
        simulate_failure_scenario()
    
    print("Test completed!")

if __name__ == "__main__":
    main()
