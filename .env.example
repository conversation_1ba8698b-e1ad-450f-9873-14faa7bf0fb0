# Server Maintenance Agent Configuration
# Copy this file to .env and edit the values

# Email Notification Settings
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SENDER_EMAIL=<EMAIL>
SENDER_PASSWORD=your-app-password
RECIPIENT_EMAIL=<EMAIL>

# Monitoring Settings
MONITOR_INTERVAL=300  # Check server resources every 5 minutes
CPU_THRESHOLD=80.0    # Alert when CPU usage exceeds 80%
MEMORY_THRESHOLD=80.0 # Alert when memory usage exceeds 80%
DISK_THRESHOLD=85.0   # Alert when disk usage exceeds 85%
ALERT_COOLDOWN=3600   # Wait 1 hour before sending another alert for the same issue

# Log Checking Settings
LOG_CHECK_INTERVAL=900  # Check logs every 15 minutes
LOG_ALERT_COOLDOWN=3600 # Wait 1 hour before sending another alert for the same log issue

# Auto-Healing Settings
AUTO_HEAL_ENABLED=true           # Enable automatic healing actions
HEAL_CHECK_INTERVAL=1800         # Run healing checks every 30 minutes
HEAL_ACTION_COOLDOWN=3600        # Wait 1 hour before attempting the same healing action again
MONITORED_SERVICES=emqx,nginx,ssh,cron  # Services to monitor and auto-heal

# EMQX Cluster Monitoring Settings
EMQX_EXPECTED_NODES=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>  # Expected cluster nodes
EMQX_CLUSTER_ALERT_COOLDOWN=3600  # Wait 1 hour before sending another cluster alert
EMQX_CLUSTER_CHECK_ENABLED=true   # Enable EMQX cluster monitoring
