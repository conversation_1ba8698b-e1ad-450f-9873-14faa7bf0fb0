#!/usr/bin/env python3
"""
Server Maintenance Agent
------------------------
An AI-powered agent that helps maintain Ubuntu servers by monitoring resources,
checking logs, and performing automatic healing actions.

Usage:
    python main.py [--monitor-only] [--log-only] [--heal-only] [--no-daemon] [--debug]

Options:
    --monitor-only    Run only the server monitoring component
    --log-only        Run only the log checking component
    --heal-only       Run only the auto-healing component
    --no-daemon       Run in foreground (don't daemonize)
    --debug           Enable debug logging
"""

import os
import sys
import time
import signal
import logging
import argparse
import threading
import traceback
from dotenv import load_dotenv

# Set up console logging first for startup messages
console_handler = logging.StreamHandler()
console_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
root_logger = logging.getLogger()
root_logger.addHandler(console_handler)
root_logger.setLevel(logging.INFO)

logger = logging.getLogger('server_agent')
logger.info(f"Starting server agent initialization. Python version: {sys.version}")
logger.info(f"Current working directory: {os.getcwd()}")

# Import daemon with error handling
try:
    import daemon
    logger.info("Successfully imported python-daemon")
except ImportError as e:
    logger.error(f"Failed to import daemon module: {e}")
    logger.error("Please install with: pip install python-daemon")
    sys.exit(1)

# Import agent components with error handling
try:
    from agent.monitor import ServerMonitor
    from agent.log_checker import LogChecker
    from agent.auto_heal import AutoHealer
    from utils.helpers import send_email_notification
    logger.info("Successfully imported all agent components")
except ImportError as e:
    logger.error(f"Failed to import agent components: {e}")
    logger.error("Make sure all required files are in the correct directories")
    sys.exit(1)

# Configure file logging
try:
    # Create logs directory if it doesn't exist
    os.makedirs('/var/log/server-agent', exist_ok=True)

    # Set up file logging with rotation
    log_file_path = '/var/log/server-agent/server_agent.log'
    log_file_handler = logging.FileHandler(log_file_path)
    log_file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    root_logger.addHandler(log_file_handler)

    # Make sure console handler is still attached for real-time viewing
    if not any(isinstance(handler, logging.StreamHandler) for handler in root_logger.handlers):
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
        root_logger.addHandler(console_handler)

    logger.info(f"File logging configured successfully to {log_file_path}")
except Exception as e:
    logger.error(f"Failed to set up file logging: {e}")
    logger.error("Will continue with console logging only")

# Load environment variables
load_dotenv()

# Global flag for graceful shutdown
running = True

def signal_handler(sig, frame):
    """Handle termination signals for graceful shutdown."""
    global running
    logger.info("Received termination signal, shutting down...")
    running = False

def run_monitor(stop_event):
    """Run the server monitor component."""
    try:
        logger.info("Initializing server monitor")
        monitor = ServerMonitor()
        logger.info("Starting server monitor with check interval: %d seconds", monitor.check_interval)

        while not stop_event.is_set():
            try:
                results = monitor.run_checks()
                logger.info(f"Monitor check completed. CPU: {results['cpu']['usage_percent']:.1f}%, "
                           f"Memory: {results['memory']['usage_percent']:.1f}%, "
                           f"Disk: {results['disk']['usage_percent']:.1f}%")
            except Exception as check_error:
                logger.error(f"Error during monitor check: {str(check_error)}")
                logger.error(traceback.format_exc())
                # Continue running despite errors in individual checks
                time.sleep(60)  # Wait a minute before retrying
                continue

            # Sleep for the check interval
            for _ in range(monitor.check_interval):
                if stop_event.is_set():
                    break
                time.sleep(1)

    except Exception as e:
        logger.error(f"Server monitor critical error: {str(e)}")
        logger.error(traceback.format_exc())
        try:
            send_email_notification("Server Monitor Error",
                                   f"The server monitor component encountered a critical error: {str(e)}\n\n"
                                   f"Traceback:\n{traceback.format_exc()}")
        except Exception as email_error:
            logger.error(f"Failed to send error notification: {str(email_error)}")

def run_log_checker(stop_event):
    """Run the log checker component."""
    try:
        logger.info("Initializing log checker")
        log_checker = LogChecker()
        logger.info("Starting log checker with check interval: %d seconds", log_checker.check_interval)

        while not stop_event.is_set():
            try:
                results = log_checker.check_all_logs()
                logger.info(f"Log check completed. Found {results.get('total_errors', 0)} potential issues")
            except Exception as check_error:
                logger.error(f"Error during log check: {str(check_error)}")
                logger.error(traceback.format_exc())
                # Continue running despite errors in individual checks
                time.sleep(60)  # Wait a minute before retrying
                continue

            # Sleep for the check interval
            for _ in range(log_checker.check_interval):
                if stop_event.is_set():
                    break
                time.sleep(1)

    except Exception as e:
        logger.error(f"Log checker critical error: {str(e)}")
        logger.error(traceback.format_exc())
        try:
            send_email_notification("Log Checker Error",
                                   f"The log checker component encountered a critical error: {str(e)}\n\n"
                                   f"Traceback:\n{traceback.format_exc()}")
        except Exception as email_error:
            logger.error(f"Failed to send error notification: {str(email_error)}")

def run_auto_healer(stop_event):
    """Run the auto-healer component."""
    try:
        logger.info("Initializing auto-healer")
        auto_healer = AutoHealer()
        logger.info("Starting auto-healer with check interval: %d seconds", auto_healer.check_interval)

        cycle = 0
        while not stop_event.is_set():
            cycle += 1
            logger.info(f"Starting auto-heal check cycle {cycle}")
            try:
                results = auto_healer.run_healing_checks()
                actions_taken = results.get('service_checks', {}).get('actions_taken', [])
                if actions_taken:
                    logger.info(f"Healing actions taken: {', '.join(actions_taken)}")
                else:
                    logger.info("Auto-heal check completed. No actions needed.")
            except Exception as check_error:
                logger.error(f"Error during auto-heal check: {str(check_error)}")
                logger.error(traceback.format_exc())
                # Continue running despite errors in individual checks
                logger.info(f"Waiting 60 seconds before retrying after error")
                time.sleep(60)  # Wait a minute before retrying
                continue

            # Sleep for the check interval
            logger.info(f"Auto-heal cycle {cycle} completed. Sleeping for {auto_healer.check_interval} seconds")
            sleep_counter = 0
            for _ in range(auto_healer.check_interval):
                sleep_counter += 1
                if sleep_counter % 10 == 0:  # Log every 10 seconds
                    logger.info(f"Auto-heal sleep: {sleep_counter}/{auto_healer.check_interval} seconds")
                if stop_event.is_set():
                    break
                time.sleep(1)
            logger.info(f"Auto-heal sleep completed. Starting next cycle.")

    except Exception as e:
        logger.error(f"Auto-healer critical error: {str(e)}")
        logger.error(traceback.format_exc())
        try:
            send_email_notification("Auto-Healer Error",
                                   f"The auto-healer component encountered a critical error: {str(e)}\n\n"
                                   f"Traceback:\n{traceback.format_exc()}")
        except Exception as email_error:
            logger.error(f"Failed to send error notification: {str(email_error)}")

def run_agent(args):
    """Run the server maintenance agent with the specified components."""
    # Set up signal handlers for graceful shutdown
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)

    # Create a stop event for threads
    stop_event = threading.Event()

    # Start the requested components
    threads = []

    if args.monitor_only or (not args.log_only and not args.heal_only):
        monitor_thread = threading.Thread(target=run_monitor, args=(stop_event,))
        monitor_thread.daemon = True
        monitor_thread.start()
        threads.append(monitor_thread)

    if args.log_only or (not args.monitor_only and not args.heal_only):
        log_thread = threading.Thread(target=run_log_checker, args=(stop_event,))
        log_thread.daemon = True
        log_thread.start()
        threads.append(log_thread)

    if args.heal_only or (not args.monitor_only and not args.log_only):
        heal_thread = threading.Thread(target=run_auto_healer, args=(stop_event,))
        heal_thread.daemon = True
        heal_thread.start()
        threads.append(heal_thread)

    # Send startup notification
    components = []
    if args.monitor_only:
        components.append("Monitor")
    elif args.log_only:
        components.append("Log Checker")
    elif args.heal_only:
        components.append("Auto-Healer")
    else:
        components = ["Monitor", "Log Checker", "Auto-Healer"]

    send_email_notification(
        "Server Maintenance Agent Started",
        f"The server maintenance agent has been started with the following components: {', '.join(components)}"
    )

    try:
        # Keep the main thread alive until signaled to stop
        while running:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down...")
    finally:
        # Signal threads to stop
        stop_event.set()

        # Wait for threads to finish
        for thread in threads:
            thread.join(timeout=5)

        logger.info("Server maintenance agent stopped")
        send_email_notification(
            "Server Maintenance Agent Stopped",
            "The server maintenance agent has been stopped."
        )

def main():
    """Parse command line arguments and start the agent."""
    parser = argparse.ArgumentParser(description='Server Maintenance Agent')
    parser.add_argument('--monitor-only', action='store_true', help='Run only the server monitoring component')
    parser.add_argument('--log-only', action='store_true', help='Run only the log checking component')
    parser.add_argument('--heal-only', action='store_true', help='Run only the auto-healing component')
    parser.add_argument('--no-daemon', action='store_true', help='Run in foreground (don\'t daemonize)')
    parser.add_argument('--debug', action='store_true', help='Enable debug logging')
    args = parser.parse_args()

    # Set debug level if requested
    if args.debug:
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)
        logger.setLevel(logging.DEBUG)
        logger.debug("Debug logging enabled")

    # Log startup
    logger.info("Starting server maintenance agent")

    # Check if required directories exist
    if not os.path.isdir('agent'):
        logger.error("Agent directory not found. Make sure you're in the correct directory.")
        sys.exit(1)

    if not os.path.isdir('utils'):
        logger.error("Utils directory not found. Make sure you're in the correct directory.")
        sys.exit(1)

    # Get root logger for file descriptor preservation
    root_logger = logging.getLogger()

    # Run as daemon unless --no-daemon is specified
    if args.no_daemon:
        logger.info("Running in foreground mode")
        try:
            run_agent(args)
        except Exception as e:
            logger.critical(f"Critical error in main process: {str(e)}")
            logger.critical(traceback.format_exc())
            sys.exit(1)
    else:
        logger.info("Starting daemon mode")
        try:
            # Preserve file descriptors for logging
            files_preserve = []
            for handler in root_logger.handlers:
                if hasattr(handler, 'stream') and hasattr(handler.stream, 'fileno'):
                    try:
                        files_preserve.append(handler.stream.fileno())
                    except Exception as e:
                        logger.warning(f"Could not add file descriptor for handler: {e}")

            # Create daemon context
            context = daemon.DaemonContext(
                working_directory=os.getcwd(),
                umask=0o022,
                pidfile=None,
                detach_process=True,
                signal_map={
                    signal.SIGTERM: signal_handler,
                    signal.SIGINT: signal_handler
                },
                files_preserve=files_preserve
            )

            # Start daemon
            with context:
                logger.info("Daemon started successfully")
                run_agent(args)
        except Exception as e:
            logger.critical(f"Failed to start daemon: {str(e)}")
            logger.critical(traceback.format_exc())
            sys.exit(1)

if __name__ == "__main__":
    main()
