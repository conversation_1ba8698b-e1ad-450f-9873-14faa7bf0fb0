#!/usr/bin/env python3
"""
EMQX Cluster Monitoring Interval Configuration
----------------------------------------------
This script helps configure how frequently the EMQX cluster status is checked.
"""

import os
import sys
from dotenv import load_dotenv, set_key

def get_current_interval():
    """Get current cluster check interval."""
    load_dotenv()
    return int(os.getenv('HEAL_CHECK_INTERVAL', 180))  # Default from code

def format_interval(seconds):
    """Format interval in human-readable format."""
    if seconds < 60:
        return f"{seconds} seconds"
    elif seconds < 3600:
        minutes = seconds // 60
        remaining_seconds = seconds % 60
        if remaining_seconds == 0:
            return f"{minutes} minutes"
        else:
            return f"{minutes} minutes {remaining_seconds} seconds"
    else:
        hours = seconds // 3600
        remaining_minutes = (seconds % 3600) // 60
        if remaining_minutes == 0:
            return f"{hours} hours"
        else:
            return f"{hours} hours {remaining_minutes} minutes"

def show_current_config():
    """Show current configuration."""
    current_interval = get_current_interval()
    
    print("\n=== Current EMQX Cluster Monitoring Configuration ===")
    print(f"Check Interval: {format_interval(current_interval)} ({current_interval} seconds)")
    
    # Calculate checks per hour/day
    checks_per_hour = 3600 / current_interval
    checks_per_day = 86400 / current_interval
    
    print(f"Frequency: ~{checks_per_hour:.1f} checks per hour, ~{checks_per_day:.0f} checks per day")
    
    # Show recommendations
    print(f"\nRecommendations:")
    if current_interval <= 60:
        print("⚠️  Very frequent - Good for testing, may use more resources")
    elif current_interval <= 300:
        print("✅ Good for production - Balances detection speed and resource usage")
    elif current_interval <= 900:
        print("✅ Moderate - Good for stable environments")
    elif current_interval <= 1800:
        print("⚠️  Infrequent - Slower issue detection")
    else:
        print("❌ Very infrequent - May miss critical issues")
    
    print()

def get_user_interval():
    """Get desired interval from user."""
    print("Select cluster monitoring frequency:")
    print("1. Every 1 minute (60s) - Testing/Development")
    print("2. Every 2 minutes (120s) - High-frequency monitoring")
    print("3. Every 5 minutes (300s) - Recommended for production")
    print("4. Every 10 minutes (600s) - Moderate monitoring")
    print("5. Every 15 minutes (900s) - Low-frequency monitoring")
    print("6. Every 30 minutes (1800s) - Very low frequency")
    print("7. Custom interval")
    
    while True:
        try:
            choice = input("\nEnter your choice (1-7): ").strip()
            
            if choice == '1':
                return 60
            elif choice == '2':
                return 120
            elif choice == '3':
                return 300
            elif choice == '4':
                return 600
            elif choice == '5':
                return 900
            elif choice == '6':
                return 1800
            elif choice == '7':
                while True:
                    try:
                        custom = input("Enter custom interval in seconds (minimum 30): ").strip()
                        custom_int = int(custom)
                        if custom_int < 30:
                            print("❌ Minimum interval is 30 seconds")
                            continue
                        if custom_int > 86400:
                            print("❌ Maximum interval is 24 hours (86400 seconds)")
                            continue
                        return custom_int
                    except ValueError:
                        print("❌ Please enter a valid number")
            else:
                print("❌ Please enter a number between 1 and 7")
        except KeyboardInterrupt:
            print("\n\n❌ Configuration cancelled")
            sys.exit(0)

def update_interval(new_interval):
    """Update the interval in .env file."""
    env_file = '.env'
    
    # Check if .env exists
    if not os.path.exists(env_file):
        print(f"⚠️  {env_file} not found. Creating from .env.example...")
        if os.path.exists('.env.example'):
            import shutil
            shutil.copy('.env.example', env_file)
            print(f"✅ Created {env_file}")
        else:
            print(f"❌ .env.example not found. Please create {env_file} manually.")
            return False
    
    # Update the interval
    try:
        set_key(env_file, 'HEAL_CHECK_INTERVAL', str(new_interval))
        print(f"✅ Updated HEAL_CHECK_INTERVAL to {new_interval} seconds in {env_file}")
        return True
    except Exception as e:
        print(f"❌ Failed to update {env_file}: {e}")
        return False

def show_impact_analysis(new_interval):
    """Show impact analysis of the new interval."""
    print(f"\n=== Impact Analysis for {format_interval(new_interval)} ===")
    
    # Resource usage estimate
    checks_per_day = 86400 / new_interval
    print(f"📊 Monitoring frequency: ~{checks_per_day:.0f} cluster checks per day")
    
    # Detection time estimate
    avg_detection_time = new_interval / 2
    print(f"⏱️  Average issue detection time: {format_interval(int(avg_detection_time))}")
    
    # Resource impact
    if new_interval <= 60:
        resource_impact = "HIGH"
        detection_speed = "VERY FAST"
    elif new_interval <= 300:
        resource_impact = "MODERATE"
        detection_speed = "FAST"
    elif new_interval <= 900:
        resource_impact = "LOW"
        detection_speed = "MODERATE"
    else:
        resource_impact = "VERY LOW"
        detection_speed = "SLOW"
    
    print(f"💻 Resource impact: {resource_impact}")
    print(f"🚀 Issue detection speed: {detection_speed}")
    
    # Recommendations
    print(f"\n💡 Recommendations:")
    if new_interval <= 60:
        print("   - Good for development and testing")
        print("   - May generate more logs and use more CPU")
        print("   - Consider increasing for production")
    elif new_interval <= 300:
        print("   - Excellent for production environments")
        print("   - Good balance of detection speed and resource usage")
        print("   - Recommended setting")
    elif new_interval <= 900:
        print("   - Good for stable environments")
        print("   - Lower resource usage")
        print("   - Acceptable detection delay")
    else:
        print("   - Only suitable for very stable environments")
        print("   - Risk of delayed issue detection")
        print("   - Consider decreasing for critical systems")

def main():
    print("EMQX Cluster Monitoring Interval Configuration")
    print("=" * 50)
    
    # Show current configuration
    show_current_config()
    
    # Get user choice
    if len(sys.argv) > 1 and sys.argv[1] in ['--status', '-s']:
        return
    
    print("Would you like to change the cluster monitoring interval?")
    change = input("Enter 'y' to change, any other key to exit: ").strip().lower()
    
    if change not in ['y', 'yes']:
        print("👋 No changes made. Goodbye!")
        return
    
    # Get new interval
    new_interval = get_user_interval()
    
    # Show impact analysis
    show_impact_analysis(new_interval)
    
    # Confirm change
    print(f"\n🔄 Change cluster monitoring interval to {format_interval(new_interval)}?")
    confirm = input("Enter 'y' to confirm: ").strip().lower()
    
    if confirm not in ['y', 'yes']:
        print("❌ Changes cancelled")
        return
    
    # Update configuration
    if update_interval(new_interval):
        print(f"\n✅ Configuration updated successfully!")
        print(f"\n📝 Next steps:")
        print("1. Restart the server-agent to apply changes:")
        print("   sudo systemctl restart server-agent")
        print("2. Monitor the new frequency in logs:")
        print("   tail -f /var/log/server-agent/server_agent.log | grep -i 'auto-heal.*cycle'")
        print("3. Test cluster monitoring:")
        print("   python test_emqx_cluster.py")
        print("4. Real-time monitoring:")
        print("   ./monitor_emqx_cluster.sh")
    else:
        print("❌ Failed to update configuration")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Configuration cancelled. Goodbye!")
