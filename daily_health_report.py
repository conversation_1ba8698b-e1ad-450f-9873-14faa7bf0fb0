#!/usr/bin/env python3
"""
Daily Health Report Generator for Server Maintenance Agent
"""
import os
import sys
import subprocess
import datetime
import psutil

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from utils.helpers import send_email_notification

def get_command_output(command):
    """Run a command and return its output."""
    try:
        result = subprocess.run(command, shell=True, check=True, 
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE, 
                               text=True)
        return result.stdout.strip()
    except Exception as e:
        return f"Error executing command: {str(e)}"

def generate_health_report():
    """Generate a comprehensive health report."""
    report = []
    
    # Add header
    now = datetime.datetime.now()
    report.append(f"DAILY HEALTH REPORT - {now.strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("=" * 50)
    report.append("")
    
    # System information
    hostname = get_command_output("hostname")
    uptime = get_command_output("uptime")
    report.append(f"Hostname: {hostname}")
    report.append(f"Uptime: {uptime}")
    report.append("")
    
    # Resource usage
    cpu_percent = psutil.cpu_percent(interval=1)
    memory = psutil.virtual_memory()
    disk = psutil.disk_usage('/')
    
    report.append("RESOURCE USAGE")
    report.append("--------------")
    report.append(f"CPU Usage: {cpu_percent:.1f}%")
    report.append(f"Memory Usage: {memory.percent:.1f}% ({memory.used / (1024**3):.1f} GB / {memory.total / (1024**3):.1f} GB)")
    report.append(f"Disk Usage: {disk.percent:.1f}% ({disk.used / (1024**3):.1f} GB / {disk.total / (1024**3):.1f} GB)")
    report.append("")
    
    # Critical services status
    report.append("CRITICAL SERVICES STATUS")
    report.append("------------------------")
    critical_services = ["emqx", "nginx", "server-agent", "ssh", "cron", "unattended-upgrades", "rsyslog"]
    for service in critical_services:
        status = get_command_output(f"systemctl is-active {service} 2>/dev/null || echo 'inactive'")
        report.append(f"{service}: {status}")
    report.append("")
    
    # Failed services
    failed_services = get_command_output("systemctl --failed --no-legend | wc -l")
    report.append(f"Failed Services: {failed_services}")
    if int(failed_services) > 0:
        failed_details = get_command_output("systemctl --failed --no-legend")
        report.append("Failed Services Details:")
        report.append(failed_details)
    report.append("")
    
    # Security information
    failed_logins = get_command_output("grep 'Failed password' /var/log/auth.log 2>/dev/null | wc -l")
    report.append("SECURITY INFORMATION")
    report.append("--------------------")
    report.append(f"Failed Login Attempts (total): {failed_logins}")
    
    # Add brute force attack details
    if int(failed_logins) > 5:
        report.append("\nPossible Brute Force Attacks:")
        # Get IPs with multiple failed attempts
        ip_pattern = "grep 'Failed password' /var/log/auth.log | grep -oE 'from ([0-9]{1,3}\.){3}[0-9]{1,3}' | cut -d' ' -f2 | sort | uniq -c | sort -nr | head -5"
        top_ips = get_command_output(ip_pattern)
        report.append("Top IPs with failed login attempts:")
        report.append(top_ips)
        
        # Add examples of failed login attempts
        report.append("\nRecent failed login examples:")
        examples = get_command_output("grep 'Failed password' /var/log/auth.log | tail -5")
        report.append(examples)
    
    # Firewall status
    ufw_status = get_command_output("ufw status | grep Status")
    report.append(f"Firewall Status: {ufw_status}")
    
    # Open ports
    open_ports = get_command_output("ss -tuln | grep LISTEN")
    report.append("Open Ports:")
    report.append(open_ports)
    report.append("")
    
    # Return the complete report
    return "\n".join(report)

if __name__ == "__main__":
    report = generate_health_report()
    print(report)
    send_email_notification("Daily Server Health Report", report)
