#!/bin/bash
# Quick script to restart the server-agent with PM2

echo "🔄 Restarting Server Maintenance Agent..."

# Stop the current PM2 process
echo "Stopping current agent..."
pm2 stop server-agent 2>/dev/null || echo "Agent not running in PM2"

# Start the agent with new configuration
echo "Starting agent with updated configuration..."
pm2 start main.py --name server-agent --interpreter python3

# Show status
echo "📊 Current PM2 status:"
pm2 status

echo ""
echo "✅ Agent restarted! Monitor logs with:"
echo "   pm2 logs server-agent"
echo ""
echo "🔍 To check cluster monitoring specifically:"
echo "   pm2 logs server-agent | grep -i cluster"
