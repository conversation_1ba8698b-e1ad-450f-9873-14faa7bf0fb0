"""
Auto-healing module for the server maintenance agent.
Performs automatic healing actions for common server issues.
"""
import os
import time
import logging
import datetime
import re
import subprocess
import psutil
from utils.helpers import run_command, restart_service, send_email_notification

# Configure logging
logger = logging.getLogger('server_agent.auto_heal')

class AutoHealer:
    def __init__(self):
        """Initialize the auto-healer with configuration from environment variables."""
        # Load configuration from environment variables or use defaults
        # Check interval: how often to check services (in seconds)
        # - Too frequent (e.g., 30s): Higher resource usage, faster detection
        # - Too infrequent (e.g., 30min): Lower resource usage, slower detection
        # 2-5 minutes is typically a good balance for most services
        self.check_interval = int(os.getenv('HEAL_CHECK_INTERVAL', 180))  # default: 3 minutes (180 seconds)
        self.auto_heal_enabled = os.getenv('AUTO_HEAL_ENABLED', 'true').lower() == 'true'

        # Define services to monitor and auto-heal
        self.monitored_services = os.getenv('MONITORED_SERVICES', 'emqx,nginx,server-agent,ssh,cron,unattended-upgrades,rsyslog').split(',')

        # Define critical services with special handling
        self.critical_services = {
            'emqx': {
                'restart_command': 'emqx restart',
                'description': 'MQTT broker (core for IoT/remote control)'
            },
            'nginx': {
                'restart_command': 'systemctl restart nginx.service',
                'description': 'Web server/reverse proxy'
            }
        }

        # Define backend services that might need restart when nginx has issues
        self.backend_services = {
            'flask_app': {
                'restart_command': 'systemctl restart flask_app.service',
                'description': 'Flask application backend'
            },
            'gunicorn': {
                'restart_command': 'systemctl restart gunicorn.service',
                'description': 'Gunicorn WSGI server'
            }
        }

        # Track last healing actions
        self.last_actions = {}
        # Cooldown period: minimum time between restart attempts for the same service
        # - Too short (e.g., 1min): Risk of restart loops if service has persistent issues
        # - Too long (e.g., 1hr): Long downtime if first restart attempt fails
        # 10-15 minutes is typically a good balance
        self.action_cooldown = int(os.getenv('HEAL_ACTION_COOLDOWN', 900))  # default: 15 minutes (900 seconds)

        # Track blocked IPs to avoid duplicate blocks
        self.blocked_ips = set()

        # Disk threshold for emergency cleanup
        self.disk_emergency_threshold = 85.0  # percentage

        if self.auto_heal_enabled:
            logger.info(f"Auto-healer initialized with monitored services: {', '.join(self.monitored_services)}")
        else:
            logger.info("Auto-healer initialized in monitoring-only mode (auto-healing disabled)")
        # EMQX cluster monitoring configuration
        self.cluster_check_enabled = os.getenv('EMQX_CLUSTER_CHECK_ENABLED', 'true').lower() == 'true'
        self.cluster_alert_cooldown = int(os.getenv('EMQX_CLUSTER_ALERT_COOLDOWN', 3600))  # seconds
        self.emqx_expected_nodes = [s.strip() for s in os.getenv('EMQX_EXPECTED_NODES', '').split(',') if s.strip()]
        self.last_emqx_cluster_alert = datetime.datetime.min

        if self.cluster_check_enabled and self.emqx_expected_nodes:
            logger.info(f"EMQX cluster monitoring enabled. Expected nodes: {', '.join(self.emqx_expected_nodes)}")
        elif self.cluster_check_enabled:
            logger.info("EMQX cluster monitoring enabled. Will alert on any stopped nodes.")
        else:
            logger.info("EMQX cluster monitoring disabled.")


    def check_service_status(self, service_name):
        """
        Check if a service is running.

        Args:
            service_name (str): Name of the service to check

        Returns:
            bool: True if service is running, False otherwise
        """
        success, output = run_command(f"systemctl is-active {service_name}")
        return success and "active" in output

    def _check_emqx_cluster(self):
        """
        Check EMQX cluster status using `emqx_ctl cluster status` and send alerts if nodes are missing.
        Respects EMQX_CLUSTER_ALERT_COOLDOWN and EMQX_EXPECTED_NODES if provided.
        """
        if not self.cluster_check_enabled:
            return None

        try:
            success, output = run_command("emqx_ctl cluster status")
            if not success:
                logger.warning(f"Failed to get EMQX cluster status: {output}")
                # If we can't get cluster status, send an alert about this issue
                now = datetime.datetime.now()
                if (now - self.last_emqx_cluster_alert).total_seconds() > self.cluster_alert_cooldown:
                    self.last_emqx_cluster_alert = now
                    message = (
                        "Unable to retrieve EMQX cluster status.\n\n"
                        f"Command failed: emqx_ctl cluster status\n"
                        f"Error output: {output}\n\n"
                        "This could indicate EMQX is not running or there's a connectivity issue."
                    )
                    send_email_notification("EMQX Cluster Status Check Failed", message)
                return None

            logger.debug(f"EMQX cluster status output: {output}")

            # Parse running_nodes and stopped_nodes from output like:
            # Cluster status: #{running_nodes => ['<EMAIL>', ...], stopped_nodes => []}
            def parse_nodes(section):
                m = re.search(rf"{section}\s*=>\s*\[(.*?)\]", output, re.S)
                if not m:
                    return []
                content = m.group(1)
                return re.findall(r"'([^']+)'", content)

            running_nodes = parse_nodes('running_nodes')
            stopped_nodes = parse_nodes('stopped_nodes')

            logger.info(f"EMQX cluster status - Running: {len(running_nodes)} nodes, Stopped: {len(stopped_nodes)} nodes")
            logger.debug(f"Running nodes: {running_nodes}")
            logger.debug(f"Stopped nodes: {stopped_nodes}")

            now = datetime.datetime.now()
            should_alert = False
            alert_reason_lines = []
            alert_severity = "WARNING"

            # If expected nodes provided, check for missing nodes
            if self.emqx_expected_nodes:
                missing = [n for n in self.emqx_expected_nodes if n not in running_nodes]
                unexpected = [n for n in running_nodes if n not in self.emqx_expected_nodes]

                if missing:
                    should_alert = True
                    alert_severity = "CRITICAL" if len(missing) > len(self.emqx_expected_nodes) // 2 else "WARNING"
                    alert_reason_lines.append(f"Missing expected nodes ({len(missing)}): {', '.join(missing)}")

                if unexpected:
                    should_alert = True
                    alert_reason_lines.append(f"Unexpected nodes in cluster ({len(unexpected)}): {', '.join(unexpected)}")

                # Check if we have less than half the expected nodes (critical situation)
                if len(running_nodes) < len(self.emqx_expected_nodes) // 2:
                    alert_severity = "CRITICAL"
                    alert_reason_lines.append(f"CRITICAL: Only {len(running_nodes)} of {len(self.emqx_expected_nodes)} expected nodes are running")

            else:
                # Without explicit expected list, alert if there are any stopped nodes
                if stopped_nodes:
                    should_alert = True
                    alert_reason_lines.append(f"Stopped nodes detected ({len(stopped_nodes)}): {', '.join(stopped_nodes)}")

            # Additional checks
            if len(running_nodes) == 0:
                should_alert = True
                alert_severity = "CRITICAL"
                alert_reason_lines.append("CRITICAL: No running nodes in cluster!")
            elif len(running_nodes) == 1 and len(self.emqx_expected_nodes) > 1:
                should_alert = True
                alert_severity = "WARNING"
                alert_reason_lines.append("WARNING: Only one node running in multi-node cluster")

            if should_alert and (now - self.last_emqx_cluster_alert).total_seconds() > self.cluster_alert_cooldown:
                self.last_emqx_cluster_alert = now

                # Build detailed message
                message_parts = [
                    f"EMQX cluster {alert_severity.lower()} detected.\n"
                ]

                if self.emqx_expected_nodes:
                    message_parts.append(f"Expected nodes ({len(self.emqx_expected_nodes)}): {', '.join(self.emqx_expected_nodes)}")

                message_parts.extend([
                    f"Running nodes ({len(running_nodes)}): {', '.join(running_nodes) if running_nodes else 'NONE'}",
                    f"Stopped nodes ({len(stopped_nodes)}): {', '.join(stopped_nodes) if stopped_nodes else 'NONE'}",
                    "",
                    "Issues detected:",
                    "\n".join(f"• {reason}" for reason in alert_reason_lines),
                    "",
                    "Raw cluster status output:",
                    output[:1000] + ("..." if len(output) > 1000 else "")
                ])

                message = "\n".join(message_parts)
                subject = f"EMQX Cluster {alert_severity} Alert"
                send_email_notification(subject, message)

                logger.warning(f"EMQX cluster alert sent: {'; '.join(alert_reason_lines)}")
            elif should_alert:
                logger.info(f"EMQX cluster issues detected but alert cooldown active: {'; '.join(alert_reason_lines)}")

            # Return parsed info for potential logging/diagnostics by caller
            return {
                'running_nodes': running_nodes,
                'stopped_nodes': stopped_nodes,
                'expected_nodes': self.emqx_expected_nodes,
                'healthy': not should_alert,
                'alert_reasons': alert_reason_lines
            }
        except Exception as e:
            logger.error(f"Error checking EMQX cluster status: {str(e)}")
            return None

    def check_and_heal_services(self):
        """
        Check the status of monitored services and restart them if needed.

        Returns:
            dict: Results of service checks and healing actions
        """
        logger.info("Starting service check and heal cycle")
        results = {
            'timestamp': datetime.datetime.now().isoformat(),
            'services': {},
            'actions_taken': []
        }

        # First, check critical services with special handling
        for service_name, service_info in self.critical_services.items():
            if service_name in self.monitored_services:
                # For EMQX, check status directly using emqx_ctl instead of systemctl
                if service_name == "emqx":
                    # Use multiple methods to check if EMQX is running
                    logger.info("Checking EMQX status using multiple methods...")

                    # Method 1: Use emqx ping
                    try:
                        ping_output = subprocess.run(
                            "emqx ping",
                            shell=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            text=True
                        ).stdout.strip()
                        logger.info(f"EMQX ping output: '{ping_output}'")

                    except Exception as e:
                        logger.error(f"Error running emqx ping: {str(e)}")
                        ping_output = ""

                    # When EMQX is running, ping responds with "pong"
                    is_running = "pong" in ping_output.lower()
                    logger.info(f"EMQX ping check: is_running = {is_running}")

                    # If EMQX is running, check cluster status
                    if is_running:
                        try:
                            cluster_info = self._check_emqx_cluster()
                            if cluster_info:
                                logger.debug(f"EMQX cluster check completed: {cluster_info['healthy']}")
                        except Exception as e:
                            logger.error(f"Error during EMQX cluster check: {str(e)}")

                    # Method 2: Use emqx_ctl status
                    try:
                        status_output = subprocess.run(
                            "emqx_ctl status",
                            shell=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            text=True
                        ).stdout.strip()
                        logger.info(f"EMQX status output: '{status_output[:100]}...'")
                    except Exception as e:
                        logger.error(f"Error running emqx_ctl status: {str(e)}")
                        status_output = ""

                    # If status shows "is started", then it's definitely running
                    if "is started" in status_output:
                        is_running = True
                        logger.info("EMQX status check: is_running = True (found 'is started')")

                    # Method 3: Check if emqx process is running
                    try:
                        ps_output = subprocess.run(
                            "ps aux | grep -v grep | grep -i 'emqx'",
                            shell=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            text=True
                        ).stdout.strip()
                        process_count = 0
                        if ps_output:
                            process_count = ps_output.count('\n') + 1
                        logger.info(f"EMQX process check: Found {process_count} processes")

                        # If we find multiple emqx processes, it might be running
                        if process_count >= 3:
                            is_running = True
                            logger.info(f"EMQX process check: is_running = True (found {process_count} processes)")
                    except Exception as e:
                        logger.error(f"Error checking EMQX processes: {str(e)}")

                    # Method 4: Check if the EMQX port is open
                    try:
                        port_output = subprocess.run(
                            "netstat -tuln | grep 1883",
                            shell=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            text=True
                        ).stdout.strip()
                        logger.info(f"EMQX port check: Port 1883 is {'OPEN' if port_output else 'CLOSED'}")

                        # If port 1883 is open, EMQX is likely running
                        if port_output and "1883" in port_output:
                            is_running = True
                            logger.info("EMQX port check: is_running = True (port 1883 is open)")
                    except Exception as e:
                        logger.error(f"Error checking EMQX port: {str(e)}")

                    logger.info(f"Final EMQX status check result: is_running = {is_running}")
                else:
                    # For other services, use systemctl
                    output = subprocess.run(
                        f"systemctl is-active {service_name} || echo 'inactive'",
                        shell=True,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True
                    ).stdout.strip()

                    is_running = output == "active"
                    logger.info(f"Checking critical service {service_name}: status = {output}")

                results['services'][service_name] = {
                    'running': is_running,
                    'action_taken': False,
                    'is_critical': True
                }

                # If critical service is not running, try to restart it immediately
                if not is_running:
                    logger.critical(f"Critical service {service_name} ({service_info['description']}) is not running!")

                    # For critical services, we have a shorter cooldown
                    now = datetime.datetime.now()
                    # Critical services get a shorter cooldown than regular services
                    # 5 minutes is a good balance for critical services like EMQX
                    critical_cooldown = min(300, self.action_cooldown)  # 5 minutes or less

                    # Check if service is in cooldown period
                    if service_name in self.last_actions:
                        seconds_since_last_action = (now - self.last_actions[service_name]).total_seconds()
                        logger.info(f"Service {service_name} was last restarted {seconds_since_last_action:.1f} seconds ago (cooldown: {critical_cooldown} seconds)")
                        in_cooldown = seconds_since_last_action <= critical_cooldown
                    else:
                        logger.info(f"Service {service_name} has not been restarted before")
                        in_cooldown = False

                    can_heal = self.auto_heal_enabled and not in_cooldown

                    if can_heal:
                        logger.info(f"Attempting to restart critical service: {service_name}")

                        # Use the specific restart command for this service
                        success, output = run_command(service_info['restart_command'])

                        # Verify the service is actually running after restart
                        # For EMQX, we need to wait longer as it takes up to 30 seconds to start
                        wait_time = 30 if service_name == "emqx" else 5
                        logger.info(f"Waiting {wait_time} seconds for {service_name} to start...")

                        # Wait and check status periodically
                        verify_output = "still_inactive"
                        for i in range(wait_time):
                            time.sleep(1)

                            # For EMQX, use emqx_ctl status and emqx ping to check
                            if service_name == "emqx":
                                try:
                                    # Use emqx ping to check if EMQX is running
                                    ping_output = subprocess.run(
                                        "emqx ping",
                                        shell=True,
                                        stdout=subprocess.PIPE,
                                        stderr=subprocess.PIPE,
                                        text=True
                                    ).stdout.strip()

                                    # When EMQX is running, ping responds with "pong"
                                    # When it's not running, it shows "not responding to pings"
                                    is_running = "pong" in ping_output.lower()

                                    # Also try emqx_ctl status as a backup check
                                    status_output = subprocess.run(
                                        "emqx_ctl status",
                                        shell=True,
                                        stdout=subprocess.PIPE,
                                        stderr=subprocess.PIPE,
                                        text=True
                                    ).stdout.strip()

                                    # If status shows "is started", then it's definitely running
                                    if "is started" in status_output:
                                        is_running = True

                                    if is_running:
                                        verify_output = "active"
                                        logger.info(f"EMQX is now running after {i+1} seconds")
                                        break
                                    else:
                                        logger.info(f"EMQX not running after {i+1} seconds. Ping: '{ping_output}', Status: '{status_output[:50]}...'")
                                except Exception as e:
                                    logger.error(f"Error checking EMQX status: {str(e)}")
                            else:
                                # For other services, use systemctl
                                current_status = subprocess.run(
                                    f"systemctl is-active {service_name} || echo 'still_inactive'",
                                    shell=True,
                                    stdout=subprocess.PIPE,
                                    stderr=subprocess.PIPE,
                                    text=True
                                ).stdout.strip()

                                if current_status == "active":
                                    verify_output = "active"
                                    logger.info(f"{service_name} is now active after {i+1} seconds")
                                    break

                        # Final check after waiting
                        if verify_output != "active":
                            if service_name == "emqx":
                                try:
                                    # Use emqx ping to check if EMQX is running
                                    ping_output = subprocess.run(
                                        "emqx ping",
                                        shell=True,
                                        stdout=subprocess.PIPE,
                                        stderr=subprocess.PIPE,
                                        text=True
                                    ).stdout.strip()

                                    # When EMQX is running, ping responds with "pong"
                                    # When it's not running, it shows "not responding to pings"
                                    is_running = "pong" in ping_output.lower()

                                    # Also try emqx_ctl status as a backup check
                                    status_output = subprocess.run(
                                        "emqx_ctl status",
                                        shell=True,
                                        stdout=subprocess.PIPE,
                                        stderr=subprocess.PIPE,
                                        text=True
                                    ).stdout.strip()

                                    # If status shows "is started", then it's definitely running
                                    if "is started" in status_output:
                                        is_running = True

                                    if is_running:
                                        verify_output = "active"
                                        logger.info(f"Final check: EMQX is running")
                                    else:
                                        logger.info(f"Final check: EMQX not running. Ping: '{ping_output}', Status: '{status_output[:50]}...'")
                                except Exception as e:
                                    logger.error(f"Error in final EMQX status check: {str(e)}")
                            else:
                                verify_output = subprocess.run(
                                    f"systemctl is-active {service_name} || echo 'still_inactive'",
                                    shell=True,
                                    stdout=subprocess.PIPE,
                                    stderr=subprocess.PIPE,
                                    text=True
                                ).stdout.strip()

                        if success and verify_output == "active":
                            logger.info(f"Successfully restarted critical service {service_name} and verified it's running")
                            self.last_actions[service_name] = now
                            results['services'][service_name]['action_taken'] = True
                            results['actions_taken'].append(f"Restarted critical service {service_name}")

                            # Send notification
                            message = f"Critical service {service_name} ({service_info['description']}) was down and has been automatically restarted."
                            send_email_notification(f"Critical Service {service_name} Auto-Healed", message)
                        else:
                            logger.error(f"Restart command for {service_name} returned success={success}, but service status is '{verify_output}'")

                            # Try a more forceful restart for emqx
                            if service_name == "emqx":
                                logger.info(f"Attempting forceful restart of emqx service")

                                # Use emqx restart for a clean restart
                                logger.info("Force restarting EMQX...")
                                try:
                                    # First try with emqx restart
                                    force_success, _ = run_command("emqx restart")
                                    logger.info("Executed emqx restart command")
                                except Exception as e:
                                    logger.error(f"Error executing emqx restart: {str(e)}")
                                    force_success = False

                                # If emqx restart fails, try the more forceful approach
                                if not force_success:
                                    logger.warning("emqx restart failed, trying more forceful approach")
                                    try:
                                        run_command("emqx stop")
                                        time.sleep(5)  # Give it more time to fully stop
                                        force_success, _ = run_command("emqx start")
                                    except Exception as e:
                                        logger.error(f"Error with forceful restart: {str(e)}")
                                        force_success = False

                                # Wait longer for EMQX to start (up to 45 seconds for force restart)
                                logger.info("Waiting up to 45 seconds for EMQX to start after force restart...")

                                # Wait and check status periodically using emqx_ctl status and emqx ping
                                verify_output = "still_inactive"
                                for i in range(45):  # Even longer wait for force restart
                                    time.sleep(1)
                                    try:
                                        # Use emqx ping to check if EMQX is running
                                        ping_output = subprocess.run(
                                            "emqx ping",
                                            shell=True,
                                            stdout=subprocess.PIPE,
                                            stderr=subprocess.PIPE,
                                            text=True
                                        ).stdout.strip()

                                        # When EMQX is running, ping responds with "pong"
                                        # When it's not running, it shows "not responding to pings"
                                        is_running = "pong" in ping_output.lower()

                                        # Also try emqx_ctl status as a backup check
                                        status_output = subprocess.run(
                                            "emqx_ctl status",
                                            shell=True,
                                            stdout=subprocess.PIPE,
                                            stderr=subprocess.PIPE,
                                            text=True
                                        ).stdout.strip()

                                        # If status shows "is started", then it's definitely running
                                        if "is started" in status_output:
                                            is_running = True

                                        if is_running:
                                            verify_output = "active"
                                            logger.info(f"EMQX is now running after force restart (took {i+1} seconds)")
                                            break
                                        else:
                                            logger.info(f"EMQX not running after force restart ({i+1} seconds). Ping: '{ping_output}', Status: '{status_output[:50]}...'")
                                    except Exception as e:
                                        logger.error(f"Error checking EMQX status after force restart: {str(e)}")

                                # Final check
                                if verify_output != "active":
                                    try:
                                        # Use emqx ping to check if EMQX is running
                                        ping_output = subprocess.run(
                                            "emqx ping",
                                            shell=True,
                                            stdout=subprocess.PIPE,
                                            stderr=subprocess.PIPE,
                                            text=True
                                        ).stdout.strip()

                                        # When EMQX is running, ping responds with "pong"
                                        # When it's not running, it shows "not responding to pings"
                                        is_running = "pong" in ping_output.lower()

                                        # Also try emqx_ctl status as a backup check
                                        status_output = subprocess.run(
                                            "emqx_ctl status",
                                            shell=True,
                                            stdout=subprocess.PIPE,
                                            stderr=subprocess.PIPE,
                                            text=True
                                        ).stdout.strip()

                                        # If status shows "is started", then it's definitely running
                                        if "is started" in status_output:
                                            is_running = True

                                        if is_running:
                                            verify_output = "active"
                                            logger.info(f"Final check after force restart: EMQX is running")
                                        else:
                                            logger.info(f"Final check after force restart: EMQX not running. Ping: '{ping_output}', Status: '{status_output[:50]}...'")
                                    except Exception as e:
                                        logger.error(f"Error in final EMQX status check after force restart: {str(e)}")

                                if force_success and verify_output == "active":
                                    logger.info(f"Successfully force-restarted emqx service and verified it's running")
                                    self.last_actions[service_name] = now
                                    results['services'][service_name]['action_taken'] = True
                                    results['actions_taken'].append(f"Force-restarted critical service {service_name}")

                                    # Send notification
                                    message = f"Critical service {service_name} ({service_info['description']}) was down and has been force-restarted."
                                    send_email_notification(f"Critical Service {service_name} Force-Restarted", message)
                                else:
                                    logger.error(f"Force restart of emqx failed. Status: {verify_output}")
                                    # Send notification about failed restart
                                    message = f"Critical service {service_name} ({service_info['description']}) is down and could not be automatically restarted even with force restart.\n\nStatus: {verify_output}"
                                    send_email_notification(f"Critical Service {service_name} Down - URGENT ACTION REQUIRED", message)
                            else:
                                logger.error(f"Failed to restart critical service {service_name}: {output}")
                                # Send notification about failed restart
                                message = f"Critical service {service_name} ({service_info['description']}) is down and could not be automatically restarted.\n\nError: {output}"
                                send_email_notification(f"Critical Service {service_name} Down - URGENT ACTION REQUIRED", message)
                    else:
                        if not self.auto_heal_enabled:
                            logger.info(f"Auto-healing disabled, not restarting critical service {service_name}")
                        else:
                            logger.info(f"Cooldown period not elapsed for critical service {service_name}, not restarting")

                        # Send notification if we haven't recently
                        if service_name not in self.last_actions or (now - self.last_actions[service_name]).total_seconds() > critical_cooldown:
                            message = f"Critical service {service_name} ({service_info['description']}) is down. Auto-healing is {'disabled' if not self.auto_heal_enabled else 'in cooldown period'}."
                            send_email_notification(f"Critical Service {service_name} Down - URGENT ACTION REQUIRED", message)

        # Now check regular services
        for service in self.monitored_services:
            service = service.strip()
            if not service or service in self.critical_services:
                continue  # Skip empty services or already processed critical services

            # Check if service is running
            is_running = self.check_service_status(service)

            results['services'][service] = {
                'running': is_running,
                'action_taken': False,
                'is_critical': False
            }

            # If service is not running, try to restart it
            if not is_running:
                logger.warning(f"Service {service} is not running")

                # Check if we can auto-heal
                now = datetime.datetime.now()
                can_heal = (
                    self.auto_heal_enabled and
                    (service not in self.last_actions or
                     (now - self.last_actions[service]).total_seconds() > self.action_cooldown)
                )

                if can_heal:
                    logger.info(f"Attempting to restart {service}")

                    # Restart the service
                    if restart_service(service):
                        self.last_actions[service] = now
                        results['services'][service]['action_taken'] = True
                        results['actions_taken'].append(f"Restarted {service}")

                        # Send notification
                        message = f"Service {service} was down and has been automatically restarted."
                        send_email_notification(f"Service {service} Auto-Healed", message)
                    else:
                        # Send notification about failed restart
                        message = f"Service {service} is down and could not be automatically restarted."
                        send_email_notification(f"Service {service} Down - Action Required", message)
                else:
                    if not self.auto_heal_enabled:
                        logger.info(f"Auto-healing disabled, not restarting {service}")
                    else:
                        logger.info(f"Cooldown period not elapsed for {service}, not restarting")

                    # Send notification if we haven't recently
                    if service not in self.last_actions or (now - self.last_actions[service]).total_seconds() > self.action_cooldown:
                        message = f"Service {service} is down. Auto-healing is {'disabled' if not self.auto_heal_enabled else 'in cooldown period'}."
                        send_email_notification(f"Service {service} Down - Action Required", message)

        return results

    def clean_old_logs(self):
        """
        Clean up old log files to free disk space.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info("Cleaning old log files")

            # Find log files older than 30 days
            success, output = run_command("find /var/log -type f -name '*.gz' -o -name '*.old' -o -name '*.1' -mtime +30")

            if not success:
                logger.error(f"Failed to find old log files: {output}")
                return False

            # If no files found, we're done
            if not output.strip():
                logger.info("No old log files to clean")
                return True

            # Count files to be removed
            files = output.strip().split('\n')
            logger.info(f"Found {len(files)} old log files to clean")

            # Remove old log files
            success, output = run_command("find /var/log -type f -name '*.gz' -o -name '*.old' -o -name '*.1' -mtime +30 -delete")

            if not success:
                logger.error(f"Failed to remove old log files: {output}")
                return False

            logger.info("Successfully cleaned old log files")
            return True

        except Exception as e:
            logger.error(f"Error cleaning old logs: {str(e)}")
            return False

    def clear_package_cache(self):
        """
        Clear APT package cache to free disk space.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info("Clearing APT package cache")

            # Clean APT cache
            success, output = run_command("apt-get clean")

            if not success:
                logger.error(f"Failed to clean APT cache: {output}")
                return False

            logger.info("Successfully cleared APT package cache")
            return True

        except Exception as e:
            logger.error(f"Error clearing package cache: {str(e)}")
            return False

    def perform_disk_cleanup(self):
        """
        Perform disk cleanup actions to free space.

        Returns:
            dict: Results of cleanup actions
        """
        results = {
            'timestamp': datetime.datetime.now().isoformat(),
            'actions': {}
        }

        # Clean old logs
        results['actions']['clean_logs'] = self.clean_old_logs()

        # Clear package cache
        results['actions']['clear_package_cache'] = self.clear_package_cache()

        return results

    def check_nginx_logs_for_errors(self):
        """
        Check nginx logs for 502 Bad Gateway errors and restart backend services if needed.

        Returns:
            dict: Results of the check and actions taken
        """
        results = {
            'found_errors': False,
            'actions_taken': []
        }

        try:
            # Check for 502 Bad Gateway errors in nginx error log
            success, output = run_command("grep -i '502 Bad Gateway' /var/log/nginx/error.log | tail -10")

            if success and output.strip():
                logger.warning(f"Found 502 Bad Gateway errors in nginx logs")
                results['found_errors'] = True

                # Check if we can auto-heal
                now = datetime.datetime.now()
                can_heal = (
                    self.auto_heal_enabled and
                    ('nginx_502_fix' not in self.last_actions or
                     (now - self.last_actions['nginx_502_fix']).total_seconds() > self.action_cooldown)
                )

                if can_heal:
                    logger.info("Attempting to restart backend services to fix 502 errors")
                    self.last_actions['nginx_502_fix'] = now

                    # Try to restart each backend service
                    for service_name, service_info in self.backend_services.items():
                        logger.info(f"Restarting {service_name}: {service_info['description']}")
                        success, output = run_command(service_info['restart_command'])

                        if success:
                            logger.info(f"Successfully restarted {service_name}")
                            results['actions_taken'].append(f"Restarted {service_name}")
                        else:
                            logger.error(f"Failed to restart {service_name}: {output}")

                    # Send notification
                    message = f"Nginx 502 Bad Gateway errors detected. Backend services have been restarted."
                    send_email_notification("Nginx 502 Errors Fixed", message)
                else:
                    logger.info("Not restarting backend services due to cooldown period or auto-healing disabled")

        except Exception as e:
            logger.error(f"Error checking nginx logs: {str(e)}")

        return results

    def check_disk_emergency(self):
        """
        Check if disk usage is above emergency threshold and perform immediate cleanup.

        Returns:
            dict: Results of the check and actions taken
        """
        results = {
            'emergency_detected': False,
            'actions_taken': []
        }

        try:
            # Get disk usage for root partition
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent

            # Check if disk usage exceeds emergency threshold
            if disk_percent >= self.disk_emergency_threshold:
                logger.critical(f"Disk usage at {disk_percent}% exceeds emergency threshold of {self.disk_emergency_threshold}%")
                results['emergency_detected'] = True

                # Check if we can auto-heal
                now = datetime.datetime.now()
                can_heal = (
                    self.auto_heal_enabled and
                    ('disk_emergency' not in self.last_actions or
                     (now - self.last_actions['disk_emergency']).total_seconds() > 1800)  # 30 minutes cooldown for emergency
                )

                if can_heal:
                    logger.info("Performing emergency disk cleanup")
                    self.last_actions['disk_emergency'] = now

                    # Clean old logs
                    if self.clean_old_logs():
                        results['actions_taken'].append("Cleaned old log files")

                    # Clear package cache
                    if self.clear_package_cache():
                        results['actions_taken'].append("Cleared package cache")

                    # Rotate logs
                    success, output = run_command("logrotate -f /etc/logrotate.conf")
                    if success:
                        logger.info("Forced log rotation")
                        results['actions_taken'].append("Forced log rotation")
                    else:
                        logger.error(f"Failed to force log rotation: {output}")

                    # Clean temp files
                    success, output = run_command("find /tmp -type f -atime +1 -delete")
                    if success:
                        logger.info("Cleaned temporary files")
                        results['actions_taken'].append("Cleaned temporary files")
                    else:
                        logger.error(f"Failed to clean temporary files: {output}")

                    # Send notification
                    message = f"Emergency disk cleanup performed. Disk usage was at {disk_percent}%.\n\nActions taken:\n" + "\n".join(results['actions_taken'])
                    send_email_notification("Emergency Disk Cleanup", message)
                else:
                    logger.info("Not performing emergency disk cleanup due to cooldown period or auto-healing disabled")

        except Exception as e:
            logger.error(f"Error checking disk emergency: {str(e)}")

        return results

    def check_auth_log_for_brute_force(self):
        """
        Check auth.log for repeated failed password attempts and block IPs if needed.

        Returns:
            dict: Results of the check and actions taken
        """
        results = {
            'brute_force_detected': False,
            'actions_taken': []
        }

        try:
            # Get failed password attempts from auth.log
            success, output = run_command("grep 'Failed password' /var/log/auth.log | tail -100")

            if success and output.strip():
                # Extract IPs from failed password attempts
                ip_pattern = re.compile(r'from (\d+\.\d+\.\d+\.\d+)')
                ip_matches = ip_pattern.findall(output)

                # Count occurrences of each IP
                ip_counts = {}
                for ip in ip_matches:
                    if ip in ip_counts:
                        ip_counts[ip] += 1
                    else:
                        ip_counts[ip] = 1

                # Check for IPs with more than 5 failed attempts
                suspicious_ips = {ip: count for ip, count in ip_counts.items() if count >= 5 and ip not in self.blocked_ips}

                if suspicious_ips:
                    logger.warning(f"Detected possible brute force attempts from IPs: {suspicious_ips}")
                    results['brute_force_detected'] = True

                    # Check if we can auto-heal
                    now = datetime.datetime.now()
                    can_heal = (
                        self.auto_heal_enabled and
                        ('brute_force_block' not in self.last_actions or
                         (now - self.last_actions['brute_force_block']).total_seconds() > self.action_cooldown)
                    )

                    if can_heal:
                        logger.info("Blocking suspicious IPs")
                        self.last_actions['brute_force_block'] = now

                        # Block each suspicious IP
                        for ip, count in suspicious_ips.items():
                            # Check if UFW is available
                            success, _ = run_command("command -v ufw")

                            if success:
                                # Block IP using UFW
                                block_cmd = f"ufw deny from {ip} to any"
                                success, output = run_command(block_cmd)

                                if success:
                                    logger.info(f"Blocked IP {ip} using UFW (had {count} failed attempts)")
                                    results['actions_taken'].append(f"Blocked IP {ip} using UFW")
                                    self.blocked_ips.add(ip)
                                else:
                                    logger.error(f"Failed to block IP {ip}: {output}")
                            else:
                                # UFW not available, send alert to admin
                                logger.warning(f"UFW not available, cannot block IP {ip}")
                                results['actions_taken'].append(f"Alerted admin about IP {ip} (UFW not available)")

                        # Send notification
                        message = f"Detected possible brute force attempts.\n\nSuspicious IPs:\n"
                        for ip, count in suspicious_ips.items():
                            message += f"- {ip}: {count} failed attempts\n"
                        message += f"\nActions taken:\n" + "\n".join(results['actions_taken'])
                        send_email_notification("Brute Force Attempts Detected", message)
                    else:
                        logger.info("Not blocking suspicious IPs due to cooldown period or auto-healing disabled")

        except Exception as e:
            logger.error(f"Error checking auth log for brute force attempts: {str(e)}")

        return results

    def run_healing_checks(self):
        """
        Run all healing checks and actions.

        Returns:
            dict: Results of all healing actions
        """
        logger.info("Running healing checks...")
        results = {
            'timestamp': datetime.datetime.now().isoformat(),
            'service_checks': {},
            'disk_cleanup': {},
            'custom_checks': {}
        }

        # Check and heal services
        service_results = self.check_and_heal_services()
        results['service_checks'] = service_results

        # Check nginx logs for 502 errors
        nginx_results = self.check_nginx_logs_for_errors()
        results['custom_checks']['nginx_502'] = nginx_results

        # Check disk emergency
        disk_emergency_results = self.check_disk_emergency()
        results['custom_checks']['disk_emergency'] = disk_emergency_results

        # Check auth log for brute force attempts
        brute_force_results = self.check_auth_log_for_brute_force()
        results['custom_checks']['brute_force'] = brute_force_results

        # Perform regular disk cleanup every 24 hours
        now = datetime.datetime.now()
        if ('disk_cleanup' not in self.last_actions or
            (now - self.last_actions['disk_cleanup']).total_seconds() > 86400):  # 24 hours

            self.last_actions['disk_cleanup'] = now
            cleanup_results = self.perform_disk_cleanup()
            results['disk_cleanup'] = cleanup_results

        return results

    def monitor_continuously(self):
        """
        Run healing checks continuously at the specified interval.
        """
        logger.info(f"Starting continuous healing checks with {self.check_interval} second intervals")

        try:
            cycle = 0
            while True:
                cycle += 1
                logger.info(f"Starting healing check cycle {cycle}")
                self.run_healing_checks()
                logger.info(f"Completed healing check cycle {cycle}, sleeping for {self.check_interval} seconds")

                # Sleep until next check
                time.sleep(self.check_interval)

        except KeyboardInterrupt:
            logger.info("Healing checks stopped by user")
        except Exception as e:
            logger.error(f"Healing checks stopped due to error: {str(e)}")
