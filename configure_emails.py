#!/usr/bin/env python3
"""
Email Configuration Helper for Server Maintenance Agent
-------------------------------------------------------
This script helps configure which types of email notifications to enable/disable.
"""

import os
import sys
from dotenv import load_dotenv, set_key

def load_current_config():
    """Load current email configuration."""
    load_dotenv()
    
    config = {
        'ENABLE_MONITORING_EMAILS': os.getenv('ENABLE_MONITORING_EMAILS', 'false').lower() == 'true',
        'ENABLE_LOG_CHECKER_EMAILS': os.getenv('ENABLE_LOG_CHECKER_EMAILS', 'false').lower() == 'true',
        'ENABLE_AUTO_HEAL_EMAILS': os.getenv('ENABLE_AUTO_HEAL_EMAILS', 'false').lower() == 'true',
        'ENABLE_SYSTEM_EMAILS': os.getenv('ENABLE_SYSTEM_EMAILS', 'false').lower() == 'true',
        'ENABLE_CLUSTER_EMAILS': os.getenv('ENABLE_CLUSTER_EMAILS', 'true').lower() == 'true',
    }
    
    return config

def display_current_config(config):
    """Display current email configuration."""
    print("\n=== Current Email Configuration ===")
    print(f"📊 Monitoring Emails (CPU/Memory/Disk/Network): {'✅ Enabled' if config['ENABLE_MONITORING_EMAILS'] else '❌ Disabled'}")
    print(f"📋 Log Checker Emails (Errors/Security): {'✅ Enabled' if config['ENABLE_LOG_CHECKER_EMAILS'] else '❌ Disabled'}")
    print(f"🔧 Auto-Heal Emails (Service Restarts): {'✅ Enabled' if config['ENABLE_AUTO_HEAL_EMAILS'] else '❌ Disabled'}")
    print(f"⚙️  System Emails (Startup/Shutdown/Errors): {'✅ Enabled' if config['ENABLE_SYSTEM_EMAILS'] else '❌ Disabled'}")
    print(f"🔗 Cluster Emails (EMQX Cluster Monitoring): {'✅ Enabled' if config['ENABLE_CLUSTER_EMAILS'] else '❌ Disabled'}")
    print()

def get_user_choice(prompt, default='n'):
    """Get yes/no choice from user."""
    while True:
        choice = input(f"{prompt} [y/N]: ").strip().lower()
        if not choice:
            choice = default
        if choice in ['y', 'yes']:
            return True
        elif choice in ['n', 'no']:
            return False
        else:
            print("Please enter 'y' for yes or 'n' for no.")

def configure_emails():
    """Interactive email configuration."""
    print("Email Configuration Helper")
    print("=" * 40)
    
    # Check if .env file exists
    env_file = '.env'
    if not os.path.exists(env_file):
        print(f"⚠️  {env_file} file not found. Creating from .env.example...")
        if os.path.exists('.env.example'):
            import shutil
            shutil.copy('.env.example', env_file)
            print(f"✅ Created {env_file} from .env.example")
        else:
            print(f"❌ .env.example not found. Please create {env_file} manually.")
            return
    
    # Load current configuration
    config = load_current_config()
    display_current_config(config)
    
    print("Configure email notifications:")
    print("Note: EMQX Cluster emails are recommended to stay enabled for critical infrastructure monitoring.\n")
    
    # Configure each email type
    email_types = [
        ('ENABLE_MONITORING_EMAILS', '📊 Enable monitoring emails (CPU/Memory/Disk/Network alerts)?'),
        ('ENABLE_LOG_CHECKER_EMAILS', '📋 Enable log checker emails (Error logs, security alerts)?'),
        ('ENABLE_AUTO_HEAL_EMAILS', '🔧 Enable auto-heal emails (Service restart notifications)?'),
        ('ENABLE_SYSTEM_EMAILS', '⚙️  Enable system emails (Agent startup/shutdown/errors)?'),
        ('ENABLE_CLUSTER_EMAILS', '🔗 Enable cluster emails (EMQX cluster monitoring)?'),
    ]
    
    new_config = {}
    
    for key, prompt in email_types:
        current_value = config[key]
        print(f"\nCurrent setting: {key} = {'true' if current_value else 'false'}")
        
        if key == 'ENABLE_CLUSTER_EMAILS':
            print("⚠️  Cluster emails are highly recommended for EMQX infrastructure monitoring!")
            
        new_value = get_user_choice(prompt, 'y' if current_value else 'n')
        new_config[key] = new_value
    
    # Show summary of changes
    print("\n=== Configuration Summary ===")
    changes_made = False
    
    for key in email_types:
        key_name = key[0]
        old_value = config[key_name]
        new_value = new_config[key_name]
        
        status = "✅ Enabled" if new_value else "❌ Disabled"
        change_indicator = ""
        
        if old_value != new_value:
            changes_made = True
            change_indicator = " (CHANGED)"
        
        print(f"{key_name}: {status}{change_indicator}")
    
    if not changes_made:
        print("\n✅ No changes made to configuration.")
        return
    
    # Confirm changes
    print(f"\n{'⚠️  ' if not new_config['ENABLE_CLUSTER_EMAILS'] else ''}Save these changes to {env_file}?")
    if not new_config['ENABLE_CLUSTER_EMAILS']:
        print("WARNING: Disabling cluster emails means you won't be notified of EMQX cluster issues!")
    
    if get_user_choice("Confirm changes", 'y'):
        # Save changes to .env file
        for key, value in new_config.items():
            set_key(env_file, key, 'true' if value else 'false')
        
        print(f"\n✅ Configuration saved to {env_file}")
        print("\n📝 Next steps:")
        print("1. Restart the server-agent to apply changes:")
        print("   sudo systemctl restart server-agent")
        print("2. Monitor logs to verify configuration:")
        print("   tail -f /var/log/server-agent/server_agent.log")
        
        if new_config['ENABLE_CLUSTER_EMAILS']:
            print("3. Test cluster monitoring:")
            print("   python test_emqx_cluster.py")
    else:
        print("\n❌ Changes cancelled.")

def show_help():
    """Show help information."""
    print("""
Email Configuration Helper

This script helps you configure which types of email notifications 
the Server Maintenance Agent should send.

Email Types:
- Monitoring Emails: CPU, Memory, Disk, Network usage alerts
- Log Checker Emails: System log errors, security alerts
- Auto-Heal Emails: Service restart notifications
- System Emails: Agent startup, shutdown, error notifications
- Cluster Emails: EMQX cluster monitoring (recommended to keep enabled)

Usage:
    python configure_emails.py          # Interactive configuration
    python configure_emails.py --help   # Show this help
    python configure_emails.py --status # Show current status only

The configuration is saved to the .env file.
""")

def show_status():
    """Show current email configuration status."""
    config = load_current_config()
    display_current_config(config)

def main():
    if len(sys.argv) > 1:
        if sys.argv[1] in ['--help', '-h']:
            show_help()
            return
        elif sys.argv[1] in ['--status', '-s']:
            show_status()
            return
        else:
            print(f"Unknown option: {sys.argv[1]}")
            print("Use --help for usage information.")
            return
    
    configure_emails()

if __name__ == "__main__":
    main()
