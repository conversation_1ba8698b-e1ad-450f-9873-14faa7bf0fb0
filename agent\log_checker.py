"""
Log checker module for the server maintenance agent.
Analyzes system logs for errors and issues.
"""
import os
import re
import time
import logging
import datetime
from collections import defaultdict
from utils.helpers import send_email_notification, run_command

# Configure logging
logger = logging.getLogger('server_agent.log_checker')

class LogChecker:
    def __init__(self):
        """Initialize the log checker with configuration from environment variables."""
        # Load configuration from environment variables or use defaults
        self.check_interval = int(os.getenv('LOG_CHECK_INTERVAL', 900))  # seconds (default: 15 minutes)

        # Define log files to monitor
        self.log_files = {
            'syslog': '/var/log/syslog',
            'auth': '/var/log/auth.log',
            'nginx_error': '/var/log/nginx/error.log',
            'nginx_access': '/var/log/nginx/access.log',
            'emqx': '/var/log/emqx/emqx.log.1',
            'kern': '/var/log/kern.log',
            'dmesg': '/var/log/dmesg'
        }

        # Define error patterns to look for
        self.error_patterns = {
            'critical': re.compile(r'(critical|emergency|alert|fatal|panic)', re.IGNORECASE),
            'error': re.compile(r'(error|fail|failed|failure)', re.IGNORECASE),
            'warning': re.compile(r'(warning|warn)', re.IGNORECASE),
            'oom_killer': re.compile(r'Out of memory: Kill process|Killed process'),
            'disk_full': re.compile(r'No space left on device'),
            'permission_denied': re.compile(r'Permission denied'),
            'segfault': re.compile(r'segfault'),
            'authentication_failure': re.compile(r'authentication failure|Failed password'),
            'brute_force': re.compile(r'Failed password.*from.*'),
            'service_failed': re.compile(r'Failed to start|systemd.*failed'),
            'nginx_error': re.compile(r'(502|504|error|critical)', re.IGNORECASE),
            'emqx_error': re.compile(r'(crash|error|exception|failed)', re.IGNORECASE),
            'firewall': re.compile(r'UFW BLOCK')
        }

        # Track last read position for each log file
        self.last_positions = {}

        # Track last alert time for each error type
        self.last_alerts = defaultdict(lambda: datetime.datetime.min)
        self.alert_cooldown = int(os.getenv('LOG_ALERT_COOLDOWN', 3600))  # seconds (default: 1 hour)

        logger.info("Log checker initialized")

    def check_log_file(self, log_name, log_path):
        """
        Check a specific log file for errors.

        Args:
            log_name (str): Name of the log file
            log_path (str): Path to the log file

        Returns:
            dict: Dictionary with error counts and details
        """
        if not os.path.exists(log_path):
            logger.debug(f"Log file {log_path} does not exist, skipping")
            return {'exists': False, 'errors': {}}

        try:
            # Get file size
            file_size = os.path.getsize(log_path)

            # If we haven't seen this file before, start from the end
            if log_path not in self.last_positions:
                self.last_positions[log_path] = file_size

            # If file was rotated (size is smaller than last position), start from beginning
            if file_size < self.last_positions[log_path]:
                self.last_positions[log_path] = 0

            # If no new content, skip
            if file_size == self.last_positions[log_path]:
                return {'exists': True, 'errors': {}}

            # Open file and seek to last position
            with open(log_path, 'r', errors='replace') as f:
                f.seek(self.last_positions[log_path])

                # Read new content
                new_content = f.read()

                # Update last position
                self.last_positions[log_path] = file_size

            # Count errors by type
            error_counts = defaultdict(int)
            error_details = defaultdict(list)

            # Split content into lines
            lines = new_content.splitlines()

            # Check each line for error patterns
            for line in lines:
                for error_type, pattern in self.error_patterns.items():
                    if pattern.search(line):
                        error_counts[error_type] += 1

                        # Store up to 5 examples of each error type
                        if len(error_details[error_type]) < 5:
                            error_details[error_type].append(line.strip())

            # Log summary
            if sum(error_counts.values()) > 0:
                logger.info(f"Found {sum(error_counts.values())} potential issues in {log_name}")

                # Send alerts for significant issues
                now = datetime.datetime.now()

                # Critical errors
                if error_counts['critical'] > 0 and (now - self.last_alerts['critical']).total_seconds() > self.alert_cooldown:
                    self.last_alerts['critical'] = now
                    message = (f"Found {error_counts['critical']} critical errors in {log_name}.\n\n"
                              f"Examples:\n" + "\n".join(error_details['critical']))
                    send_email_notification(f"Critical Errors in {log_name}", message)

                # OOM killer
                if error_counts['oom_killer'] > 0 and (now - self.last_alerts['oom_killer']).total_seconds() > self.alert_cooldown:
                    self.last_alerts['oom_killer'] = now
                    message = (f"Out of Memory killer was triggered {error_counts['oom_killer']} times.\n\n"
                              f"Examples:\n" + "\n".join(error_details['oom_killer']))
                    send_email_notification("Out of Memory Events Detected", message)

                # Disk full
                if error_counts['disk_full'] > 0 and (now - self.last_alerts['disk_full']).total_seconds() > self.alert_cooldown:
                    self.last_alerts['disk_full'] = now
                    message = (f"Disk full errors detected {error_counts['disk_full']} times.\n\n"
                              f"Examples:\n" + "\n".join(error_details['disk_full']))
                    send_email_notification("Disk Full Errors Detected", message)

                # Brute force attempts
                if error_counts['brute_force'] > 5 and (now - self.last_alerts['brute_force']).total_seconds() > self.alert_cooldown:
                    self.last_alerts['brute_force'] = now
                    message = (f"Detected {error_counts['brute_force']} failed login attempts, possible brute force attack.\n\n"
                              f"Examples:\n" + "\n".join(error_details['brute_force']))
                    send_email_notification("Possible Brute Force Attack", message)

            return {
                'exists': True,
                'errors': {
                    'counts': dict(error_counts),
                    'details': {k: v for k, v in error_details.items() if v}
                }
            }

        except Exception as e:
            logger.error(f"Error checking log file {log_path}: {str(e)}")
            return {'exists': True, 'error': str(e), 'errors': {}}

    def check_journal_logs(self):
        """
        Check systemd journal logs for errors.

        Returns:
            dict: Dictionary with error counts and details
        """
        try:
            # Get recent journal entries with priority warning or higher
            success, output = run_command("journalctl -p warning -n 100 --no-pager")

            if not success:
                logger.error(f"Failed to get journal logs: {output}")
                return {'errors': {}}

            # Count errors by type
            error_counts = defaultdict(int)
            error_details = defaultdict(list)

            # Split content into lines
            lines = output.splitlines()

            # Check each line for error patterns
            for line in lines:
                for error_type, pattern in self.error_patterns.items():
                    if pattern.search(line):
                        error_counts[error_type] += 1

                        # Store up to 5 examples of each error type
                        if len(error_details[error_type]) < 5:
                            error_details[error_type].append(line.strip())

            # Log summary
            if sum(error_counts.values()) > 0:
                logger.info(f"Found {sum(error_counts.values())} potential issues in systemd journal")

                # Send alerts for service failures
                now = datetime.datetime.now()
                if error_counts['service_failed'] > 0 and (now - self.last_alerts['service_failed']).total_seconds() > self.alert_cooldown:
                    self.last_alerts['service_failed'] = now
                    message = (f"Found {error_counts['service_failed']} service failures in systemd journal.\n\n"
                              f"Examples:\n" + "\n".join(error_details['service_failed']))
                    send_email_notification("Service Failures Detected", message)

            return {
                'errors': {
                    'counts': dict(error_counts),
                    'details': {k: v for k, v in error_details.items() if v}
                }
            }

        except Exception as e:
            logger.error(f"Error checking journal logs: {str(e)}")
            return {'error': str(e), 'errors': {}}

    def check_all_logs(self):
        """
        Check all configured log files for errors.

        Returns:
            dict: Results of all log checks
        """
        results = {
            'timestamp': datetime.datetime.now().isoformat(),
            'logs': {}
        }

        # Check each log file
        for log_name, log_path in self.log_files.items():
            results['logs'][log_name] = self.check_log_file(log_name, log_path)

        # Check systemd journal
        results['logs']['journal'] = self.check_journal_logs()

        # Calculate total errors
        total_errors = 0
        for log_data in results['logs'].values():
            if 'errors' in log_data and 'counts' in log_data['errors']:
                total_errors += sum(log_data['errors']['counts'].values())

        results['total_errors'] = total_errors

        if total_errors > 0:
            logger.warning(f"Found a total of {total_errors} potential issues across all logs")
        else:
            logger.info("No issues found in logs")

        return results

    def monitor_continuously(self):
        """
        Run log checks continuously at the specified interval.
        """
        logger.info(f"Starting continuous log monitoring with {self.check_interval} second intervals")

        try:
            while True:
                self.check_all_logs()

                # Sleep until next check
                time.sleep(self.check_interval)

        except KeyboardInterrupt:
            logger.info("Log monitoring stopped by user")
        except Exception as e:
            logger.error(f"Log monitoring stopped due to error: {str(e)}")
