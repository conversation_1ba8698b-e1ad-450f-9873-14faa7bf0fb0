#!/bin/bash
# Script to view server-agent logs in real-time

# Define colors for better readability
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to display usage information
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo "View server-agent logs in real-time with optional filtering."
    echo ""
    echo "Options:"
    echo "  -h, --help       Show this help message"
    echo "  -e, --errors     Show only ERROR and CRITICAL log entries"
    echo "  -a, --auto-heal  Show only auto-heal related logs"
    echo "  -m, --monitor    Show only monitoring related logs"
    echo "  -l, --log-check  Show only log checker related logs"
    echo "  -n, --lines N    Show the last N lines (default: 50)"
    echo "  -f, --follow     Follow the log file (like tail -f)"
    echo ""
    echo "Examples:"
    echo "  $0 -e -f         Show and follow only error logs"
    echo "  $0 -a -n 100     Show last 100 lines of auto-heal logs"
    echo "  $0 -f            Show and follow all logs"
}

# Default values
SHOW_ERRORS_ONLY=false
COMPONENT_FILTER=""
LINES=50
FOLLOW=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        -e|--errors)
            SHOW_ERRORS_ONLY=true
            shift
            ;;
        -a|--auto-heal)
            COMPONENT_FILTER="auto_heal"
            shift
            ;;
        -m|--monitor)
            COMPONENT_FILTER="monitor"
            shift
            ;;
        -l|--log-check)
            COMPONENT_FILTER="log_checker"
            shift
            ;;
        -n|--lines)
            LINES="$2"
            shift 2
            ;;
        -f|--follow)
            FOLLOW=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check if log file exists
LOG_FILE="/var/log/server-agent/server_agent.log"
if [ ! -f "$LOG_FILE" ]; then
    echo -e "${RED}Error: Log file not found at $LOG_FILE${NC}"
    echo "Make sure the server-agent is running and has proper permissions."
    exit 1
fi

# Build the grep filter based on options
GREP_FILTER=""

if [ "$SHOW_ERRORS_ONLY" = true ]; then
    GREP_FILTER="ERROR\|CRITICAL"
fi

if [ ! -z "$COMPONENT_FILTER" ]; then
    if [ ! -z "$GREP_FILTER" ]; then
        GREP_FILTER="$GREP_FILTER\|$COMPONENT_FILTER"
    else
        GREP_FILTER="$COMPONENT_FILTER"
    fi
fi

# Build the tail command
TAIL_CMD="tail"
if [ "$FOLLOW" = true ]; then
    TAIL_CMD="$TAIL_CMD -f"
fi
TAIL_CMD="$TAIL_CMD -n $LINES $LOG_FILE"

# Add color highlighting
if [ ! -z "$GREP_FILTER" ]; then
    echo -e "${CYAN}Showing logs filtered by: $GREP_FILTER${NC}"
    $TAIL_CMD | grep --color=always -i "$GREP_FILTER" | sed -E "s/.*ERROR.*/$(echo -e "${RED}&${NC}")/g" | sed -E "s/.*CRITICAL.*/$(echo -e "${RED}&${NC}")/g" | sed -E "s/.*WARNING.*/$(echo -e "${YELLOW}&${NC}")/g" | sed -E "s/.*INFO.*/$(echo -e "${GREEN}&${NC}")/g"
else
    echo -e "${CYAN}Showing all logs${NC}"
    $TAIL_CMD | sed -E "s/.*ERROR.*/$(echo -e "${RED}&${NC}")/g" | sed -E "s/.*CRITICAL.*/$(echo -e "${RED}&${NC}")/g" | sed -E "s/.*WARNING.*/$(echo -e "${YELLOW}&${NC}")/g" | sed -E "s/.*INFO.*/$(echo -e "${GREEN}&${NC}")/g"
fi
