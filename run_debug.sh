#!/bin/bash
# Script to run the server-agent in debug mode with real-time logging

# Define colors for better readability
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to display usage information
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo "Run the server-agent in debug mode with real-time logging."
    echo ""
    echo "Options:"
    echo "  -h, --help         Show this help message"
    echo "  -m, --monitor-only Run only the server monitoring component"
    echo "  -l, --log-only     Run only the log checking component"
    echo "  -a, --heal-only    Run only the auto-healing component"
    echo ""
    echo "Examples:"
    echo "  $0                 Run all components in debug mode"
    echo "  $0 -a              Run only the auto-healing component in debug mode"
}

# Default values
COMPONENTS=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        -m|--monitor-only)
            COMPONENTS="--monitor-only"
            shift
            ;;
        -l|--log-only)
            COMPONENTS="--log-only"
            shift
            ;;
        -a|--heal-only)
            COMPONENTS="--heal-only"
            shift
            ;;
        *)
            echo "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check if the script is run with sudo
if [ "$EUID" -ne 0 ]; then
    echo -e "${YELLOW}Warning: This script should be run with sudo to ensure proper permissions.${NC}"
    echo "Try: sudo $0 $COMPONENTS"
    read -p "Continue anyway? (y/n) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Create logs directory if it doesn't exist
mkdir -p /var/log/server-agent

# Check if the server-agent is already running
if pgrep -f "python.*main.py" > /dev/null; then
    echo -e "${YELLOW}Warning: The server-agent appears to be already running.${NC}"
    echo "You may want to stop it first with: sudo systemctl stop server-agent"
    read -p "Continue anyway? (y/n) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Run the server-agent in debug mode with real-time logging
echo -e "${CYAN}Starting server-agent in debug mode...${NC}"
echo -e "${CYAN}Press Ctrl+C to stop${NC}"
echo

# Run the server-agent with the specified components
python3 main.py $COMPONENTS --debug --no-daemon

# This point is reached when the server-agent is stopped
echo
echo -e "${GREEN}Server-agent stopped${NC}"
