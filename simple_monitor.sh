#!/bin/bash
# Simple script to monitor EMQX auto-healing in real-time

# Check if log file exists
LOG_FILE="/var/log/server-agent/server_agent.log"
if [ ! -f "$LOG_FILE" ]; then
    echo "Error: Log file not found at $LOG_FILE"
    echo "Make sure the server-agent is running and has proper permissions."
    exit 1
fi

# Display header
echo "=== EMQX Auto-Healing Monitor ==="
echo "Press Ctrl+C to exit"
echo

# Display current EMQX status
echo "Current EMQX status:"
systemctl status emqx | grep Active
echo

# Monitor logs in real-time
echo "=== Auto-Healing Logs ==="
tail -n 100 -f "$LOG_FILE" | grep --color=always -i "auto_heal.*emqx"
