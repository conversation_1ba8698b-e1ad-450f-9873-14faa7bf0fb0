#!/bin/bash
# Script to monitor the auto-healing process in real-time

# Define colors for better readability
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to display usage information
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo "Monitor the auto-healing process in real-time."
    echo ""
    echo "Options:"
    echo "  -h, --help       Show this help message"
    echo "  -s, --service    Filter logs for a specific service (e.g., emqx, nginx)"
    echo "  -n, --lines N    Show the last N lines (default: 100)"
    echo ""
    echo "Examples:"
    echo "  $0                Monitor all auto-healing activities"
    echo "  $0 -s emqx        Monitor auto-healing for the EMQX service"
}

# Default values
SERVICE_FILTER=""
LINES=100

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        -s|--service)
            SERVICE_FILTER="$2"
            shift 2
            ;;
        -n|--lines)
            LINES="$2"
            shift 2
            ;;
        *)
            echo "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check if log file exists
LOG_FILE="/var/log/server-agent/server_agent.log"
if [ ! -f "$LOG_FILE" ]; then
    echo -e "${RED}Error: Log file not found at $LOG_FILE${NC}"
    echo "Make sure the server-agent is running and has proper permissions."
    exit 1
fi

# Display header
echo -e "${CYAN}=== Auto-Healing Process Monitor ===${NC}"
echo -e "${CYAN}Press Ctrl+C to exit${NC}"
echo

# Build the grep filter
GREP_FILTER="auto_heal"
if [ ! -z "$SERVICE_FILTER" ]; then
    GREP_FILTER="$GREP_FILTER.*$SERVICE_FILTER"
    echo -e "${CYAN}Monitoring auto-healing for service: ${YELLOW}$SERVICE_FILTER${NC}"
else
    echo -e "${CYAN}Monitoring all auto-healing activities${NC}"
fi

# Function to display service status
display_service_status() {
    echo
    echo -e "${CYAN}=== Current Service Status ===${NC}"
    
    # Get list of monitored services
    SERVICES=$(grep -i "monitored services" "$LOG_FILE" | tail -1 | sed -E 's/.*monitored services: (.*)/\1/')
    
    if [ -z "$SERVICES" ]; then
        echo -e "${YELLOW}No monitored services found in logs${NC}"
        return
    fi
    
    # Display status for each service
    IFS=',' read -ra SERVICE_ARRAY <<< "$SERVICES"
    for SERVICE in "${SERVICE_ARRAY[@]}"; do
        SERVICE=$(echo "$SERVICE" | xargs)  # Trim whitespace
        STATUS=$(systemctl is-active "$SERVICE" 2>/dev/null || echo "unknown")
        
        if [ "$STATUS" = "active" ]; then
            echo -e "${SERVICE}: ${GREEN}$STATUS${NC}"
        elif [ "$STATUS" = "inactive" ]; then
            echo -e "${SERVICE}: ${RED}$STATUS${NC}"
        else
            echo -e "${SERVICE}: ${YELLOW}$STATUS${NC}"
        fi
    done
    echo
}

# Display initial service status
display_service_status

# Monitor logs in real-time
echo -e "${CYAN}=== Auto-Healing Logs ===${NC}"
tail -n $LINES -f "$LOG_FILE" | grep --color=always -i "$GREP_FILTER" | 
    sed -E "s/.*ERROR.*/$(echo -e "${RED}&${NC}")/g" | 
    sed -E "s/.*CRITICAL.*/$(echo -e "${RED}&${NC}")/g" | 
    sed -E "s/.*WARNING.*/$(echo -e "${YELLOW}&${NC}")/g" | 
    sed -E "s/.*INFO.*/$(echo -e "${GREEN}&${NC}")/g" |
    sed -E "s/.*successfully.*restarted.*/$(echo -e "${BLUE}&${NC}")/g" |
    sed -E "s/.*not running.*/$(echo -e "${PURPLE}&${NC}")/g"
