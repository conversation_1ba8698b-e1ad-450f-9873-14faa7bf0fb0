#!/bin/bash
# Server Maintenance Agent Installation Script

# Exit on error
set -e

echo "Server Maintenance Agent Installation"
echo "====================================="

# Check if running as root
if [ "$EUID" -ne 0 ]; then
  echo "Please run as root"
  exit 1
fi

# Check if we're on a supported system
if [ -f /etc/os-release ]; then
  . /etc/os-release
  if [[ "$ID" == "ubuntu" || "$ID" == "debian" ]]; then
    echo "Detected $PRETTY_NAME"
  else
    echo "Warning: This script is designed for Ubuntu/Debian systems."
    echo "Your system: $PRETTY_NAME"
    read -p "Continue anyway? (y/n) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
      exit 1
    fi
  fi
else
  echo "Warning: Could not detect OS."
  read -p "Continue anyway? (y/n) " -n 1 -r
  echo
  if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    exit 1
  fi
fi

# Install dependencies
echo "Installing dependencies..."
apt-get update
apt-get install -y python3 python3-pip

# Install Python packages
echo "Installing Python packages..."
pip3 install -r requirements.txt

# Configure .env file if it doesn't exist
if [ ! -f .env ]; then
  echo "Creating .env file..."
  cp .env.example .env
  echo "Please edit the .env file to configure email notifications and other settings."
fi

# Install systemd service
echo "Installing systemd service..."
cp server-agent.service /etc/systemd/system/
systemctl daemon-reload
systemctl enable server-agent

# Set permissions
echo "Setting permissions..."
chmod +x main.py

# Create log directory
echo "Creating log directory..."
mkdir -p /var/log/server-agent
touch /var/log/server-agent/server_agent.log
chmod 644 /var/log/server-agent/server_agent.log

# Create a symbolic link for the log file
ln -sf /var/log/server-agent/server_agent.log server_agent.log

echo
echo "Installation complete!"
echo
echo "Next steps:"
echo "1. Edit the .env file to configure email notifications and other settings:"
echo "   nano .env"
echo
echo "2. Start the service:"
echo "   systemctl start server-agent"
echo
echo "3. Check the status:"
echo "   systemctl status server-agent"
echo
echo "4. View logs:"
echo "   tail -f /var/log/server-agent/server_agent.log"
echo
