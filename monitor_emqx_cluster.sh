#!/bin/bash
# EMQX Cluster Monitoring Script
# Real-time monitoring of EMQX cluster status and auto-healing logs

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
LOG_FILE="/var/log/server-agent/server_agent.log"
REFRESH_INTERVAL=10

# Function to display header
show_header() {
    clear
    echo -e "${BLUE}=== EMQX Cluster Monitoring Dashboard ===${NC}"
    echo -e "${BLUE}Press Ctrl+C to exit | Refresh every ${REFRESH_INTERVAL}s${NC}"
    echo
}

# Function to check EMQX status
check_emqx_status() {
    echo -e "${YELLOW}EMQX Service Status:${NC}"
    if systemctl is-active --quiet emqx; then
        echo -e "  ${GREEN}✓ EMQX service is running${NC}"
        
        # Check if EMQX responds to ping
        if emqx ping >/dev/null 2>&1; then
            echo -e "  ${GREEN}✓ EMQX responds to ping${NC}"
        else
            echo -e "  ${RED}✗ EMQX not responding to ping${NC}"
        fi
    else
        echo -e "  ${RED}✗ EMQX service is not running${NC}"
        return 1
    fi
    echo
}

# Function to show cluster status
show_cluster_status() {
    echo -e "${YELLOW}Cluster Status:${NC}"
    
    if ! command -v emqx_ctl >/dev/null 2>&1; then
        echo -e "  ${RED}✗ emqx_ctl command not found${NC}"
        return 1
    fi
    
    cluster_output=$(emqx_ctl cluster status 2>/dev/null)
    if [ $? -eq 0 ]; then
        # Parse running nodes
        running_nodes=$(echo "$cluster_output" | grep -A 10 "running_nodes" | grep -o "'[^']*'" | tr -d "'" | wc -l)
        stopped_nodes=$(echo "$cluster_output" | grep -A 10 "stopped_nodes" | grep -o "'[^']*'" | tr -d "'" | wc -l)
        
        echo -e "  ${GREEN}✓ Cluster status retrieved${NC}"
        echo -e "  Running nodes: ${GREEN}$running_nodes${NC}"
        echo -e "  Stopped nodes: ${RED}$stopped_nodes${NC}"
        
        # Show individual nodes
        echo
        echo -e "${YELLOW}Running Nodes:${NC}"
        echo "$cluster_output" | grep -A 20 "running_nodes" | grep -o "'[^']*'" | tr -d "'" | while read node; do
            if [ ! -z "$node" ]; then
                echo -e "  ${GREEN}✓ $node${NC}"
            fi
        done
        
        if [ $stopped_nodes -gt 0 ]; then
            echo
            echo -e "${YELLOW}Stopped Nodes:${NC}"
            echo "$cluster_output" | grep -A 20 "stopped_nodes" | grep -o "'[^']*'" | tr -d "'" | while read node; do
                if [ ! -z "$node" ]; then
                    echo -e "  ${RED}✗ $node${NC}"
                fi
            done
        fi
    else
        echo -e "  ${RED}✗ Failed to get cluster status${NC}"
        return 1
    fi
    echo
}

# Function to show recent cluster-related logs
show_recent_logs() {
    echo -e "${YELLOW}Recent Cluster Monitoring Logs:${NC}"
    
    if [ ! -f "$LOG_FILE" ]; then
        echo -e "  ${RED}✗ Log file not found: $LOG_FILE${NC}"
        return 1
    fi
    
    # Show last 10 cluster-related log entries
    tail -n 100 "$LOG_FILE" | grep -i "cluster\|emqx" | tail -n 10 | while IFS= read -r line; do
        timestamp=$(echo "$line" | grep -o '[0-9]\{4\}-[0-9]\{2\}-[0-9]\{2\} [0-9]\{2\}:[0-9]\{2\}:[0-9]\{2\}')
        
        if echo "$line" | grep -qi "error\|critical\|failed"; then
            echo -e "  ${RED}$line${NC}"
        elif echo "$line" | grep -qi "warning\|alert"; then
            echo -e "  ${YELLOW}$line${NC}"
        else
            echo -e "  ${GREEN}$line${NC}"
        fi
    done
    echo
}

# Function to show configuration
show_config() {
    echo -e "${YELLOW}Configuration:${NC}"
    
    if [ -f ".env" ]; then
        expected_nodes=$(grep "EMQX_EXPECTED_NODES" .env 2>/dev/null | cut -d'=' -f2 | tr ',' '\n')
        cluster_enabled=$(grep "EMQX_CLUSTER_CHECK_ENABLED" .env 2>/dev/null | cut -d'=' -f2)
        alert_cooldown=$(grep "EMQX_CLUSTER_ALERT_COOLDOWN" .env 2>/dev/null | cut -d'=' -f2)
        
        echo -e "  Cluster monitoring: ${GREEN}${cluster_enabled:-true}${NC}"
        echo -e "  Alert cooldown: ${GREEN}${alert_cooldown:-3600}s${NC}"
        
        if [ ! -z "$expected_nodes" ]; then
            echo -e "  Expected nodes:"
            echo "$expected_nodes" | while read node; do
                if [ ! -z "$node" ]; then
                    echo -e "    ${BLUE}• $node${NC}"
                fi
            done
        else
            echo -e "  ${YELLOW}⚠ No expected nodes configured${NC}"
        fi
    else
        echo -e "  ${RED}✗ .env file not found${NC}"
    fi
    echo
}

# Main monitoring loop
main() {
    # Check if running as root or with sudo
    if [ "$EUID" -ne 0 ]; then
        echo -e "${YELLOW}Note: Some commands may require root privileges${NC}"
        echo
    fi
    
    while true; do
        show_header
        show_config
        check_emqx_status
        show_cluster_status
        show_recent_logs
        
        echo -e "${BLUE}Next refresh in ${REFRESH_INTERVAL} seconds...${NC}"
        sleep $REFRESH_INTERVAL
    done
}

# Handle Ctrl+C gracefully
trap 'echo -e "\n${GREEN}Monitoring stopped.${NC}"; exit 0' INT

# Check if log file exists and warn if not
if [ ! -f "$LOG_FILE" ]; then
    echo -e "${YELLOW}Warning: Log file not found at $LOG_FILE${NC}"
    echo -e "${YELLOW}Make sure the server-agent is running and has proper permissions.${NC}"
    echo
fi

# Start monitoring
main
