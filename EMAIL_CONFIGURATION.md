# Email Configuration Guide

This document explains how to configure email notifications in the Server Maintenance Agent, with a focus on enabling only EMQX cluster monitoring emails while disabling other notifications.

## Overview

The Server Maintenance Agent now supports granular control over email notifications. You can enable/disable different types of alerts independently:

- **📊 Monitoring Emails**: CPU, Memory, Disk, Network usage alerts
- **📋 Log Checker Emails**: System log errors, security alerts  
- **🔧 Auto-Heal Emails**: Service restart notifications
- **⚙️ System Emails**: Agent startup, shutdown, error notifications
- **🔗 Cluster Emails**: EMQX cluster monitoring (recommended to keep enabled)

## Quick Setup (Cluster Monitoring Only)

To enable only EMQX cluster monitoring emails (recommended configuration):

1. **Copy the example configuration**:
   ```bash
   cp .env.example .env
   ```

2. **Edit your `.env` file** to include:
   ```bash
   # Email Control Settings
   ENABLE_MONITORING_EMAILS=false      # Disable CPU/Memory/Disk alerts
   ENABLE_LOG_CHECKER_EMAILS=false     # Disable log checker alerts
   <PERSON><PERSON><PERSON><PERSON>_AUTO_HEAL_EMAILS=false       # Disable auto-healing notifications
   ENABLE_SYSTEM_EMAILS=false          # Disable system startup/shutdown emails
   ENABLE_CLUSTER_EMAILS=true          # Enable EMQX cluster monitoring (KEEP THIS!)
   
   # EMQX Cluster Monitoring Settings
   EMQX_EXPECTED_NODES=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
   EMQX_CLUSTER_ALERT_COOLDOWN=3600
   EMQX_CLUSTER_CHECK_ENABLED=true
   ```

3. **Configure your SMTP settings**:
   ```bash
   SMTP_SERVER=smtp.gmail.com
   SMTP_PORT=587
   SENDER_EMAIL=<EMAIL>
   SENDER_PASSWORD=your-app-password
   RECIPIENT_EMAIL=<EMAIL>
   ```

4. **Restart the agent**:
   ```bash
   sudo systemctl restart server-agent
   ```

## Interactive Configuration

Use the configuration helper script for easy setup:

```bash
# Interactive configuration
python configure_emails.py

# Show current status
python configure_emails.py --status

# Show help
python configure_emails.py --help
```

## Email Types Explained

### 🔗 Cluster Emails (RECOMMENDED: ENABLED)
- **Purpose**: Monitor EMQX cluster health
- **Triggers**: Missing nodes, cluster failures, connectivity issues
- **Importance**: Critical for MQTT infrastructure monitoring
- **Example**: "EMQX Cluster CRITICAL Alert - Missing expected nodes"

### 📊 Monitoring Emails (RECOMMENDED: DISABLED)
- **Purpose**: System resource monitoring
- **Triggers**: High CPU, memory, disk usage, network issues
- **Frequency**: Can be frequent during high load
- **Example**: "High CPU Usage Alert - 85% usage detected"

### 📋 Log Checker Emails (RECOMMENDED: DISABLED)
- **Purpose**: System log analysis
- **Triggers**: Critical errors, security events, OOM killer
- **Frequency**: Varies based on system activity
- **Example**: "Critical Errors in syslog - 5 errors detected"

### 🔧 Auto-Heal Emails (RECOMMENDED: DISABLED)
- **Purpose**: Service restart notifications
- **Triggers**: Service failures and automatic restarts
- **Frequency**: Only when services fail
- **Example**: "Service nginx Auto-Healed - automatically restarted"

### ⚙️ System Emails (RECOMMENDED: DISABLED)
- **Purpose**: Agent lifecycle notifications
- **Triggers**: Agent startup, shutdown, critical errors
- **Frequency**: Rare, only during agent lifecycle events
- **Example**: "Server Maintenance Agent Started"

## Configuration File Structure

Your `.env` file should include these sections:

```bash
# Basic SMTP Configuration
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SENDER_EMAIL=<EMAIL>
SENDER_PASSWORD=your-app-password
RECIPIENT_EMAIL=<EMAIL>

# Email Control Settings (NEW)
ENABLE_MONITORING_EMAILS=false
ENABLE_LOG_CHECKER_EMAILS=false
ENABLE_AUTO_HEAL_EMAILS=false
ENABLE_SYSTEM_EMAILS=false
ENABLE_CLUSTER_EMAILS=true

# EMQX Cluster Monitoring Settings
EMQX_EXPECTED_NODES=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
EMQX_CLUSTER_ALERT_COOLDOWN=3600
EMQX_CLUSTER_CHECK_ENABLED=true

# Other monitoring settings...
MONITOR_INTERVAL=300
CPU_THRESHOLD=80.0
MEMORY_THRESHOLD=80.0
DISK_THRESHOLD=85.0
```

## Testing Configuration

### Test Cluster Monitoring
```bash
# Test cluster monitoring functionality
python test_emqx_cluster.py

# Test with simulated failure
python test_emqx_cluster.py --simulate-failure
```

### Monitor Real-time
```bash
# Real-time cluster monitoring dashboard
./monitor_emqx_cluster.sh
```

### Check Logs
```bash
# Monitor agent logs for email notifications
tail -f /var/log/server-agent/server_agent.log | grep -i "email\|cluster"
```

## Troubleshooting

### No Cluster Emails Received
1. Check if cluster monitoring is enabled:
   ```bash
   grep ENABLE_CLUSTER_EMAILS .env
   ```

2. Verify SMTP configuration:
   ```bash
   python -c "from utils.helpers import send_cluster_email_notification; send_cluster_email_notification('Test', 'Test message')"
   ```

3. Check agent logs:
   ```bash
   tail -f /var/log/server-agent/server_agent.log | grep -i cluster
   ```

### Emails Still Being Sent
1. Verify configuration is loaded:
   ```bash
   python configure_emails.py --status
   ```

2. Restart the agent:
   ```bash
   sudo systemctl restart server-agent
   ```

3. Check for old configuration files or environment variables

### SMTP Issues
1. Test SMTP connectivity:
   ```bash
   telnet smtp.gmail.com 587
   ```

2. Verify credentials and app passwords for Gmail
3. Check firewall settings for SMTP ports

## Migration from Previous Version

If upgrading from a previous version:

1. **Backup your current `.env`**:
   ```bash
   cp .env .env.backup
   ```

2. **Add new email control settings**:
   ```bash
   echo "ENABLE_MONITORING_EMAILS=false" >> .env
   echo "ENABLE_LOG_CHECKER_EMAILS=false" >> .env
   echo "ENABLE_AUTO_HEAL_EMAILS=false" >> .env
   echo "ENABLE_SYSTEM_EMAILS=false" >> .env
   echo "ENABLE_CLUSTER_EMAILS=true" >> .env
   ```

3. **Restart the agent**:
   ```bash
   sudo systemctl restart server-agent
   ```

## Best Practices

1. **Keep cluster emails enabled** - Critical for infrastructure monitoring
2. **Disable noisy alerts** - Reduce email fatigue by disabling frequent alerts
3. **Test configuration** - Always test after making changes
4. **Monitor logs** - Check agent logs to ensure configuration is working
5. **Use appropriate cooldowns** - Set reasonable alert intervals to avoid spam
6. **Document your setup** - Keep track of your specific node names and configuration

## Security Considerations

- Use app passwords instead of regular passwords for Gmail
- Restrict SMTP access to necessary IP ranges
- Consider using dedicated email accounts for monitoring
- Regularly rotate email credentials
- Monitor for unauthorized email configuration changes
