# EMQX Cluster Monitoring

This document explains how to configure and use the enhanced EMQX cluster monitoring functionality in the Server Maintenance Agent.

## Overview

The server agent now includes comprehensive EMQX cluster monitoring that:
- Monitors cluster node status using `emqx_ctl cluster status`
- Detects missing or unexpected nodes
- Sends email alerts when cluster issues are detected
- Provides different alert levels (WARNING/CRITICAL) based on severity
- Respects cooldown periods to prevent alert spam

## Configuration

### Environment Variables

Add these settings to your `.env` file:

```bash
# EMQX Cluster Monitoring Settings
EMQX_EXPECTED_NODES=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
EMQX_CLUSTER_ALERT_COOLDOWN=3600  # Wait 1 hour before sending another cluster alert
EMQX_CLUSTER_CHECK_ENABLED=true   # Enable EMQX cluster monitoring
```

### Configuration Options

- **EMQX_EXPECTED_NODES**: Comma-separated list of expected cluster node names
  - Format: `<EMAIL>`
  - If not set, the system will only alert on stopped nodes
  - If set, the system will alert on missing expected nodes and unexpected nodes

- **EMQX_CLUSTER_ALERT_COOLDOWN**: Time in seconds between cluster alerts (default: 3600)
  - Prevents spam when cluster issues persist
  - Set to a lower value for more frequent alerts during testing

- **EMQX_CLUSTER_CHECK_ENABLED**: Enable/disable cluster monitoring (default: true)
  - Set to `false` to disable cluster monitoring entirely

## Alert Scenarios

### WARNING Alerts
- One or more expected nodes are missing (but not majority)
- Unexpected nodes detected in cluster
- Only one node running in a multi-node cluster
- Some nodes are in stopped state

### CRITICAL Alerts
- More than half of expected nodes are missing
- No running nodes in cluster
- Unable to retrieve cluster status

## Sample Alert Email

```
Subject: EMQX Cluster CRITICAL Alert

EMQX cluster critical detected.

Expected nodes (4): <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>
Running nodes (2): <EMAIL>, <EMAIL>
Stopped nodes (0): NONE

Issues detected:
• Missing expected nodes (2): <EMAIL>, <EMAIL>
• CRITICAL: Only 2 of 4 expected nodes are running

Raw cluster status output:
Cluster status: #{running_nodes =>
                      ['<EMAIL>',
                       '<EMAIL>'],
                  stopped_nodes => []}
```

## Testing

### Test Script
Use the provided test script to verify cluster monitoring:

```bash
# Basic test
python test_emqx_cluster.py

# Test with simulated failure
python test_emqx_cluster.py --simulate-failure
```

### Manual Testing
1. Check current cluster status:
   ```bash
   emqx_ctl cluster status
   ```

2. Temporarily stop a node to test alerts:
   ```bash
   # On one of the cluster nodes
   emqx stop
   ```

3. Monitor the agent logs:
   ```bash
   tail -f /var/log/server-agent/server_agent.log | grep -i cluster
   ```

## Monitoring Integration

The cluster monitoring is integrated into the auto-healing component and runs:
- Every time EMQX service status is checked
- During regular auto-healing cycles (default: every 30 minutes)
- Only when EMQX is detected as running

## Troubleshooting

### Common Issues

1. **"Failed to get EMQX cluster status"**
   - Ensure EMQX is running: `systemctl status emqx`
   - Check if `emqx_ctl` command is available
   - Verify the agent has permissions to run EMQX commands

2. **No cluster alerts despite node failures**
   - Check if `EMQX_CLUSTER_CHECK_ENABLED=true`
   - Verify `EMQX_EXPECTED_NODES` is configured correctly
   - Check alert cooldown hasn't suppressed recent alerts

3. **False positive alerts**
   - Verify node names in `EMQX_EXPECTED_NODES` match exactly
   - Check for typos in domain names
   - Ensure all expected nodes are actually part of the cluster

### Debug Mode
Enable debug logging to see detailed cluster check information:

```bash
# Run agent in debug mode
python main.py --debug --no-daemon
```

## Log Messages

The system logs various cluster monitoring events:

```
INFO: EMQX cluster monitoring enabled. Expected nodes: <EMAIL>, <EMAIL>
INFO: EMQX cluster status - Running: 4 nodes, Stopped: 0 nodes
WARNING: EMQX cluster alert sent: Missing expected nodes (1): <EMAIL>
INFO: EMQX cluster issues detected but alert cooldown active: Missing expected nodes (1): <EMAIL>
```

## Best Practices

1. **Set realistic expected nodes**: Only include nodes that should always be running
2. **Use appropriate cooldown**: Balance between timely alerts and avoiding spam
3. **Monitor logs**: Regularly check agent logs for cluster status messages
4. **Test configuration**: Use the test script after configuration changes
5. **Document node names**: Keep a record of your cluster node naming convention
