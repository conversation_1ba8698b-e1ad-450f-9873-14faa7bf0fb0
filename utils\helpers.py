"""
Helper utilities for the server maintenance agent.
"""
import os
import sys
import smtplib
import logging
import platform
import subprocess
import traceback
import json
from datetime import datetime, timezone
from pathlib import Path
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON>Multipart
from dotenv import load_dotenv

# Configure logging
logger = logging.getLogger('server_agent.utils')

# Load environment variables
try:
    load_dotenv()
    logger.info("Environment variables loaded")
except Exception as e:
    logger.error(f"Failed to load environment variables: {e}")

# Detect operating system
SYSTEM_INFO = {
    'os': platform.system(),
    'release': platform.release(),
    'hostname': platform.node()
}
logger.info(f"Running on {SYSTEM_INFO['os']} {SYSTEM_INFO['release']} ({SYSTEM_INFO['hostname']})")

# Email throttling state (global limit)
THROTTLE_STATE_FILE = Path(os.getenv('EMAIL_THROTTLE_STATE_FILE', '/tmp/server_agent_email_throttle.json'))
EMAIL_GLOBAL_COOLDOWN_SECONDS = int(os.getenv('EMAIL_GLOBAL_COOLDOWN_SECONDS', 86400))  # default: 24 hours

def _load_throttle_map():
    try:
        if THROTTLE_STATE_FILE.exists():
            data = json.loads(THROTTLE_STATE_FILE.read_text())
            if isinstance(data, dict):
                # Backward compatibility: old global format {'last_sent_ts': ...}
                if 'last_sent_ts' in data and len(data) == 1:
                    return {}
                return data
    except Exception:
        pass
    return {}

def _save_throttle_map(d):
    try:
        THROTTLE_STATE_FILE.parent.mkdir(parents=True, exist_ok=True)
        THROTTLE_STATE_FILE.write_text(json.dumps(d))
    except Exception:
        pass

def _should_send_email_now(subject: str):
    try:
        d = _load_throttle_map()
        last_ts = d.get(subject)
        if isinstance(last_ts, (int, float)):
            last_dt = datetime.fromtimestamp(last_ts, tz=timezone.utc)
            now = datetime.now(tz=timezone.utc)
            if (now - last_dt).total_seconds() < EMAIL_GLOBAL_COOLDOWN_SECONDS:
                return False
        return True
    except Exception:
        # On any error, allow sending to avoid missing critical alerts
        return True

def _record_email_sent(subject: str):
    try:
        d = _load_throttle_map()
        d[subject] = datetime.now(tz=timezone.utc).timestamp()
        _save_throttle_map(d)
    except Exception:
        pass

def send_email_notification(subject, message, force_send=False):
    """
    Send an email notification about server issues.

    NOTE: This function is now disabled for all monitoring except EMQX cluster monitoring.
    Use send_cluster_email_notification() for cluster alerts.

    Global throttle: at most one email per EMAIL_GLOBAL_COOLDOWN_SECONDS (default 24h).

    Args:
        subject (str): Email subject
        message (str): Email message body
        force_send (bool): If True, bypass the email disable check (used for cluster monitoring)

    Returns:
        bool: True if successful, False otherwise
    """
    # Check if this is a cluster-related email or if force_send is True
    is_cluster_email = any(keyword in subject.lower() for keyword in ['cluster', 'emqx'])

    if not force_send and not is_cluster_email:
        # Check specific email enable flags based on the type of alert
        email_enabled = False

        # Determine the type of alert and check corresponding flag
        if any(keyword in subject.lower() for keyword in ['cpu', 'memory', 'disk', 'network']):
            email_enabled = os.getenv('ENABLE_MONITORING_EMAILS', 'false').lower() == 'true'
        elif any(keyword in subject.lower() for keyword in ['log', 'critical', 'oom', 'brute force', 'service failure']):
            email_enabled = os.getenv('ENABLE_LOG_CHECKER_EMAILS', 'false').lower() == 'true'
        elif any(keyword in subject.lower() for keyword in ['auto-heal', 'service', 'restart', 'cleanup']):
            email_enabled = os.getenv('ENABLE_AUTO_HEAL_EMAILS', 'false').lower() == 'true'
        elif any(keyword in subject.lower() for keyword in ['started', 'stopped', 'error', 'agent']):
            email_enabled = os.getenv('ENABLE_SYSTEM_EMAILS', 'false').lower() == 'true'

        if not email_enabled:
            logger.info(f"Email notifications disabled for this alert type. Skipping: {subject}")
            return False
    try:
        # Per-subject throttle check
        if not _should_send_email_now(subject):
            logger.info(f"Email throttle active for subject '{subject}': skipping email send")
            return False

        # Get email configuration from environment variables
        smtp_server = os.getenv('SMTP_SERVER')
        smtp_port = int(os.getenv('SMTP_PORT', 587))
        sender_email = os.getenv('SENDER_EMAIL')
        sender_password = os.getenv('SENDER_PASSWORD')
        recipient_email = os.getenv('RECIPIENT_EMAIL')

        # Check if email configuration is available
        if not all([smtp_server, sender_email, sender_password, recipient_email]):
            logger.warning("Email configuration is incomplete. Check .env file.")
            logger.debug(f"SMTP_SERVER: {'Set' if smtp_server else 'Not set'}, "
                        f"SENDER_EMAIL: {'Set' if sender_email else 'Not set'}, "
                        f"SENDER_PASSWORD: {'Set' if sender_password else 'Not set'}, "
                        f"RECIPIENT_EMAIL: {'Set' if recipient_email else 'Not set'}")
            return False

        # Add system information to the message
        full_message = (
            f"{message}\n\n"
            f"---\n"
            f"Server: {SYSTEM_INFO['hostname']}\n"
            f"OS: {SYSTEM_INFO['os']} {SYSTEM_INFO['release']}\n"
            f"Time: {subprocess.check_output('date', shell=True).decode().strip()}\n"
        )

        # Create message
        msg = MIMEMultipart()
        msg['From'] = sender_email
        msg['To'] = recipient_email
        msg['Subject'] = f"[SERVER ALERT] {subject}"

        # Add message body
        msg.attach(MIMEText(full_message, 'plain'))

        logger.debug(f"Connecting to SMTP server {smtp_server}:{smtp_port}")

        # Connect to SMTP server and send email
        with smtplib.SMTP(smtp_server, smtp_port, timeout=10) as server:
            server.starttls()
            server.login(sender_email, sender_password)
            server.send_message(msg)

        _record_email_sent()
        logger.info(f"Email notification sent: {subject}")
        return True

    except Exception as e:
        logger.error(f"Failed to send email notification: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def send_cluster_email_notification(subject, message):
    """
    Send an email notification specifically for EMQX cluster issues.
    This function bypasses the general email disable check.

    Args:
        subject (str): Email subject
        message (str): Email message body

    Returns:
        bool: True if successful, False otherwise
    """
    return send_email_notification(subject, message, force_send=True)

def run_command(command, timeout=60):
    """
    Run a shell command and return the output.

    Args:
        command (str): Command to run
        timeout (int): Timeout in seconds (default: 60)

    Returns:
        tuple: (success, output) where success is a boolean and output is the command output
    """
    logger.debug(f"Running command: {command}")

    try:
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            timeout=timeout
        )
        logger.debug(f"Command completed successfully")
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed: {command}, Error: {e.stderr}")
        return False, e.stderr
    except subprocess.TimeoutExpired as e:
        logger.error(f"Command timed out after {timeout} seconds: {command}")
        return False, f"Command timed out after {timeout} seconds"
    except Exception as e:
        logger.error(f"Error running command: {command}, Error: {str(e)}")
        logger.error(traceback.format_exc())
        return False, str(e)

def restart_service(service_name):
    """
    Restart a system service.

    Args:
        service_name (str): Name of the service to restart

    Returns:
        bool: True if successful, False otherwise
    """
    logger.info(f"Attempting to restart service: {service_name}")

    try:
        # Use different commands based on the operating system
        if SYSTEM_INFO['os'] == 'Linux':
            # Check which init system is in use
            if os.path.exists('/bin/systemctl') or os.path.exists('/usr/bin/systemctl'):
                success, output = run_command(f"sudo systemctl restart {service_name}")
            elif os.path.exists('/etc/init.d/{service_name}'):
                success, output = run_command(f"sudo /etc/init.d/{service_name} restart")
            else:
                success, output = run_command(f"sudo service {service_name} restart")
        elif SYSTEM_INFO['os'] == 'Darwin':  # macOS
            success, output = run_command(f"sudo launchctl stop {service_name} && sudo launchctl start {service_name}")
        else:
            logger.error(f"Unsupported operating system: {SYSTEM_INFO['os']}")
            return False

        if success:
            logger.info(f"Successfully restarted {service_name}")
            return True
        else:
            logger.error(f"Failed to restart {service_name}: {output}")
            return False

    except Exception as e:
        logger.error(f"Error restarting service {service_name}: {str(e)}")
        logger.error(traceback.format_exc())
        return False
