# Setting Up Cron Jobs for Server Maintenance Agent

To set up the scheduled tasks for daily health reports and weekly summaries, follow these steps:

## 1. Install the cron jobs

```bash
# View the crontab file
cat crontab.txt

# Install the cron jobs (make sure to run as root)
crontab crontab.txt
```

## 2. Verify the cron jobs are installed

```bash
crontab -l
```

You should see the following entries:
```
# Daily health report at 7 AM
0 7 * * * cd /root/server-agent && python3 daily_health_report.py

# Weekly summary on Sunday at 8 AM
0 8 * * 0 cd /root/server-agent && python3 weekly_summary.py
```

## 3. Test the reports

You can test the reports by running them manually:

```bash
# Test the daily health report
python3 daily_health_report.py

# Test the weekly summary
python3 weekly_summary.py
```

## Schedule Explanation

- **Daily Health Report**: Runs every day at 7:00 AM
- **Weekly Summary**: Runs every Sunday at 8:00 AM

## Troubleshooting

If the cron jobs are not running:

1. Check if cron is running:
   ```bash
   systemctl status cron
   ```

2. Check the system logs for cron errors:
   ```bash
   grep CRON /var/log/syslog
   ```

3. Make sure the scripts have execute permissions:
   ```bash
   chmod +x daily_health_report.py weekly_summary.py
   ```

4. Ensure the path to Python is correct:
   ```bash
   which python3
   ```
   
   If it's not `/usr/bin/python3`, update the crontab file with the correct path.
