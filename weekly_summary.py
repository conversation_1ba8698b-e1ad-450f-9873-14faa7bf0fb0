#!/usr/bin/env python3
"""
Weekly Summary Report Generator for Server Maintenance Agent
"""
import os
import sys
import subprocess
import datetime

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from utils.helpers import send_email_notification

def get_command_output(command):
    """Run a command and return its output."""
    try:
        result = subprocess.run(command, shell=True, check=True, 
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE, 
                               text=True)
        return result.stdout.strip()
    except Exception as e:
        return f"Error executing command: {str(e)}"

def generate_weekly_summary():
    """Generate a comprehensive weekly summary."""
    report = []
    
    # Add header
    now = datetime.datetime.now()
    report.append(f"WEEKLY SERVER SUMMARY - {now.strftime('%Y-%m-%d')}")
    report.append("=" * 50)
    report.append("")
    
    # System uptime and load
    uptime = get_command_output("uptime")
    report.append(f"Current Status: {uptime}")
    report.append("")
    
    # Service uptime
    report.append("SERVICE UPTIME")
    report.append("-------------")
    critical_services = ["emqx", "nginx", "server-agent", "ssh", "cron", "unattended-upgrades", "rsyslog"]
    for service in critical_services:
        try:
            uptime_info = get_command_output(f"systemctl show {service} -p ActiveEnterTimestamp 2>/dev/null | cut -d= -f2")
            report.append(f"{service}: {uptime_info}")
        except:
            report.append(f"{service}: Not available or not running")
    report.append("")
    
    # Unattended upgrades history
    report.append("UNATTENDED UPGRADES HISTORY")
    report.append("--------------------------")
    try:
        upgrade_log = get_command_output("grep 'Packages that will be upgraded' /var/log/unattended-upgrades/unattended-upgrades.log 2>/dev/null | tail -10")
        report.append(upgrade_log if upgrade_log else "No recent upgrades found")
    except:
        report.append("Upgrade logs not available")
    report.append("")
    
    # Reboot events
    report.append("REBOOT EVENTS")
    report.append("-------------")
    reboot_events = get_command_output("last reboot | head -5")
    report.append(reboot_events)
    report.append("")
    
    # Failed services summary
    report.append("FAILED SERVICES SUMMARY")
    report.append("----------------------")
    failed_services = get_command_output("systemctl --failed --no-legend")
    report.append(failed_services if failed_services else "No failed services")
    report.append("")
    
    # Disk usage trend
    report.append("DISK USAGE TREND")
    report.append("---------------")
    disk_usage = get_command_output("df -h /")
    report.append(disk_usage)
    report.append("")
    
    # Return the complete report
    return "\n".join(report)

if __name__ == "__main__":
    report = generate_weekly_summary()
    print(report)
    send_email_notification("Weekly Server Summary", report)
