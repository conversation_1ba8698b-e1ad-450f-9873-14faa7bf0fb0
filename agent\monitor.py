"""
Server resource monitoring module.
Monitors CPU, memory, disk, and network usage.
"""
import time
import psutil
import logging
import os
from datetime import datetime
from utils.helpers import send_email_notification

# Configure logging
logger = logging.getLogger('server_agent.monitor')

class ServerMonitor:
    def __init__(self):
        """Initialize the server monitor with threshold values from environment variables."""
        # Load threshold values from environment variables or use defaults
        self.cpu_threshold = float(os.getenv('CPU_THRESHOLD', 80.0))  # percentage
        self.memory_threshold = float(os.getenv('MEMORY_THRESHOLD', 85.0))  # percentage
        self.disk_threshold = float(os.getenv('DISK_THRESHOLD', 80.0))  # percentage
        self.check_interval = int(os.getenv('MONITOR_INTERVAL', 300))  # seconds (default: 5 minutes)

        # Memory sustained high usage tracking
        self.memory_alert_duration = int(os.getenv('MEMORY_ALERT_DURATION', 600))  # seconds (default: 10 minutes)
        self.high_memory_start_time = None  # Track when memory usage first exceeded threshold

        # Initialize alert cooldown tracking
        self.last_alerts = {
            'cpu': datetime.min,
            'memory': datetime.min,
            'memory_sustained': datetime.min,
            'disk': datetime.min,
            'network': datetime.min
        }
        self.alert_cooldown = int(os.getenv('ALERT_COOLDOWN', 3600))  # seconds (default: 1 hour)

        logger.info(f"Server monitor initialized with thresholds: CPU {self.cpu_threshold}%, "
                   f"Memory {self.memory_threshold}%, Disk {self.disk_threshold}%")

    def check_cpu(self):
        """
        Check CPU usage and alert if it exceeds the threshold.

        Returns:
            tuple: (is_healthy, usage_percentage, details)
        """
        try:
            # Get CPU usage percentage (average over 1 second)
            cpu_percent = psutil.cpu_percent(interval=1)

            # Get per-core CPU usage
            per_cpu = psutil.cpu_percent(interval=None, percpu=True)

            # Check if CPU usage exceeds threshold
            is_healthy = cpu_percent < self.cpu_threshold

            # Log CPU status
            if is_healthy:
                logger.info(f"CPU usage: {cpu_percent:.1f}% (healthy)")
            else:
                logger.warning(f"CPU usage: {cpu_percent:.1f}% (exceeds threshold of {self.cpu_threshold}%)")

                # Send alert if cooldown period has passed
                now = datetime.now()
                if (now - self.last_alerts['cpu']).total_seconds() > self.alert_cooldown:
                    self.last_alerts['cpu'] = now

                    # Get top CPU-consuming processes
                    processes = []
                    for proc in sorted(psutil.process_iter(['pid', 'name', 'cpu_percent']),
                                      key=lambda p: p.info['cpu_percent'],
                                      reverse=True)[:5]:
                        processes.append(f"{proc.info['name']} (PID: {proc.info['pid']}): {proc.info['cpu_percent']:.1f}%")

                    # Send notification
                    message = (f"CPU usage is {cpu_percent:.1f}%, which exceeds the threshold of {self.cpu_threshold}%.\n\n"
                              f"Top CPU-consuming processes:\n" + "\n".join(processes))
                    send_email_notification("High CPU Usage Alert", message)

            # Return status, usage percentage, and details
            return is_healthy, cpu_percent, {
                'per_core': per_cpu,
                'load_avg': os.getloadavg()
            }

        except Exception as e:
            logger.error(f"Error checking CPU usage: {str(e)}")
            return False, 0, {'error': str(e)}

    def check_memory(self):
        """
        Check memory usage and alert if it exceeds the threshold.

        Returns:
            tuple: (is_healthy, usage_percentage, details)
        """
        try:
            # Get memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent

            # Check if memory usage exceeds threshold
            is_healthy = memory_percent < self.memory_threshold

            # Log memory status
            if is_healthy:
                logger.info(f"Memory usage: {memory_percent:.1f}% (healthy)")
                # Reset high memory tracking if memory usage is now healthy
                self.high_memory_start_time = None
            else:
                logger.warning(f"Memory usage: {memory_percent:.1f}% (exceeds threshold of {self.memory_threshold}%)")

                # Track sustained high memory usage
                now = datetime.now()
                if self.high_memory_start_time is None:
                    # First time memory exceeded threshold
                    self.high_memory_start_time = now
                    logger.warning(f"Memory usage exceeded threshold, starting duration tracking")
                elif (now - self.high_memory_start_time).total_seconds() > self.memory_alert_duration:
                    # Memory has been high for longer than the duration threshold
                    logger.critical(f"Memory usage has been above {self.memory_threshold}% for more than {self.memory_alert_duration/60} minutes")

                    # Only send alert if cooldown period has passed
                    if (now - self.last_alerts['memory_sustained']).total_seconds() > self.alert_cooldown:
                        self.last_alerts['memory_sustained'] = now

                        # Get top memory-consuming processes
                        processes = []
                        for proc in sorted(psutil.process_iter(['pid', 'name', 'memory_percent']),
                                          key=lambda p: p.info['memory_percent'],
                                          reverse=True)[:5]:
                            processes.append(f"{proc.info['name']} (PID: {proc.info['pid']}): {proc.info['memory_percent']:.1f}%")

                        message = (f"Memory usage has been above {self.memory_threshold}% for more than {self.memory_alert_duration/60} minutes.\n"
                                  f"Current usage: {memory_percent:.1f}%\n"
                                  f"Total: {memory.total / (1024**3):.1f} GB, Available: {memory.available / (1024**3):.1f} GB\n\n"
                                  f"Top memory-consuming processes:\n" + "\n".join(processes))
                        send_email_notification("Sustained High Memory Usage Alert", message)

                # Send immediate alert if cooldown period has passed
                if (now - self.last_alerts['memory']).total_seconds() > self.alert_cooldown:
                    self.last_alerts['memory'] = now

                    # Get top memory-consuming processes
                    processes = []
                    for proc in sorted(psutil.process_iter(['pid', 'name', 'memory_percent']),
                                      key=lambda p: p.info['memory_percent'],
                                      reverse=True)[:5]:
                        processes.append(f"{proc.info['name']} (PID: {proc.info['pid']}): {proc.info['memory_percent']:.1f}%")

                    # Send notification
                    message = (f"Memory usage is {memory_percent:.1f}%, which exceeds the threshold of {self.memory_threshold}%.\n\n"
                              f"Total: {memory.total / (1024**3):.1f} GB, Available: {memory.available / (1024**3):.1f} GB\n\n"
                              f"Top memory-consuming processes:\n" + "\n".join(processes))
                    send_email_notification("High Memory Usage Alert", message)

            # Return status, usage percentage, and details
            return is_healthy, memory_percent, {
                'total_gb': memory.total / (1024**3),
                'available_gb': memory.available / (1024**3),
                'used_gb': memory.used / (1024**3)
            }

        except Exception as e:
            logger.error(f"Error checking memory usage: {str(e)}")
            return False, 0, {'error': str(e)}

    def check_disk(self):
        """
        Check disk usage and alert if it exceeds the threshold.

        Returns:
            tuple: (is_healthy, usage_percentage, details)
        """
        try:
            # Get disk usage for root partition
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent

            # Check if disk usage exceeds threshold
            is_healthy = disk_percent < self.disk_threshold

            # Log disk status
            if is_healthy:
                logger.info(f"Disk usage: {disk_percent:.1f}% (healthy)")
            else:
                logger.warning(f"Disk usage: {disk_percent:.1f}% (exceeds threshold of {self.disk_threshold}%)")

                # Send alert if cooldown period has passed
                now = datetime.now()
                if (now - self.last_alerts['disk']).total_seconds() > self.alert_cooldown:
                    self.last_alerts['disk'] = now

                    # Get all partitions
                    partitions = []
                    for part in psutil.disk_partitions(all=False):
                        if os.path.exists(part.mountpoint):
                            usage = psutil.disk_usage(part.mountpoint)
                            partitions.append(f"{part.mountpoint}: {usage.percent:.1f}% of {usage.total / (1024**3):.1f} GB")

                    # Send notification
                    message = (f"Disk usage is {disk_percent:.1f}%, which exceeds the threshold of {self.disk_threshold}%.\n\n"
                              f"Root partition: {disk.used / (1024**3):.1f} GB used out of {disk.total / (1024**3):.1f} GB\n\n"
                              f"All partitions:\n" + "\n".join(partitions))
                    send_email_notification("High Disk Usage Alert", message)

            # Return status, usage percentage, and details
            return is_healthy, disk_percent, {
                'total_gb': disk.total / (1024**3),
                'used_gb': disk.used / (1024**3),
                'free_gb': disk.free / (1024**3)
            }

        except Exception as e:
            logger.error(f"Error checking disk usage: {str(e)}")
            return False, 0, {'error': str(e)}

    def check_network(self):
        """
        Check network usage and connectivity.

        Returns:
            tuple: (is_healthy, details)
        """
        try:
            # Get network I/O statistics
            net_io = psutil.net_io_counters()

            # Check internet connectivity by pinging Google's DNS
            is_connected = False
            try:
                is_connected = os.system("ping -c 1 8.8.8.8 > /dev/null 2>&1") == 0
            except:
                pass

            # Log network status
            if is_connected:
                logger.info("Network connectivity: OK")
            else:
                logger.warning("Network connectivity: FAILED")

                # Send alert if cooldown period has passed
                now = datetime.now()
                if (now - self.last_alerts['network']).total_seconds() > self.alert_cooldown:
                    self.last_alerts['network'] = now

                    # Send notification
                    message = "Network connectivity check failed. The server cannot reach the internet."
                    send_email_notification("Network Connectivity Alert", message)

            # Return status and details
            return is_connected, {
                'bytes_sent': net_io.bytes_sent,
                'bytes_recv': net_io.bytes_recv,
                'packets_sent': net_io.packets_sent,
                'packets_recv': net_io.packets_recv,
                'is_connected': is_connected
            }

        except Exception as e:
            logger.error(f"Error checking network: {str(e)}")
            return False, {'error': str(e)}

    def run_checks(self):
        """
        Run all server health checks and return the results.

        Returns:
            dict: Results of all checks
        """
        results = {
            'timestamp': datetime.now().isoformat(),
            'cpu': {},
            'memory': {},
            'disk': {},
            'network': {}
        }

        # Run CPU check
        cpu_healthy, cpu_percent, cpu_details = self.check_cpu()
        results['cpu'] = {
            'healthy': cpu_healthy,
            'usage_percent': cpu_percent,
            'details': cpu_details
        }

        # Run memory check
        memory_healthy, memory_percent, memory_details = self.check_memory()
        results['memory'] = {
            'healthy': memory_healthy,
            'usage_percent': memory_percent,
            'details': memory_details
        }

        # Run disk check
        disk_healthy, disk_percent, disk_details = self.check_disk()
        results['disk'] = {
            'healthy': disk_healthy,
            'usage_percent': disk_percent,
            'details': disk_details
        }

        # Run network check
        network_healthy, network_details = self.check_network()
        results['network'] = {
            'healthy': network_healthy,
            'details': network_details
        }

        # Overall system health
        results['overall_healthy'] = all([
            cpu_healthy,
            memory_healthy,
            disk_healthy,
            network_healthy
        ])

        return results

    def monitor_continuously(self):
        """
        Run monitoring checks continuously at the specified interval.
        """
        logger.info(f"Starting continuous monitoring with {self.check_interval} second intervals")

        try:
            while True:
                results = self.run_checks()

                # Log overall status
                if results['overall_healthy']:
                    logger.info("Overall system health: GOOD")
                else:
                    logger.warning("Overall system health: ISSUES DETECTED")

                # Sleep until next check
                time.sleep(self.check_interval)

        except KeyboardInterrupt:
            logger.info("Monitoring stopped by user")
        except Exception as e:
            logger.error(f"Monitoring stopped due to error: {str(e)}")
