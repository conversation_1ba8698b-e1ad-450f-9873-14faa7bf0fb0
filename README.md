# Server Maintenance Agent

An AI-powered agent that helps maintain Ubuntu servers by monitoring resources, checking logs, and performing automatic healing actions.

## Features

- **Server Monitoring**: Tracks CPU, memory, disk, and network usage
- **Log Analysis**: Scans system logs for errors and suspicious activities
- **Auto-Healing**: Automatically restarts failed services and performs maintenance tasks
- **Email Notifications**: Sends alerts when issues are detected

## Requirements

- Python 3.6+
- Ubuntu Server (tested on 18.04, 20.04, 22.04)
- Required Python packages:
  - psutil
  - python-daemon
  - python-dotenv

## Installation

### Automatic Installation

The easiest way to install the agent is using the provided installation script:

1. Clone this repository:
   ```
   git clone https://github.com/yourusername/server-agent.git
   cd server-agent
   ```

2. Run the installation script as root:
   ```
   sudo bash install.sh
   ```

3. Configure the agent by editing the `.env` file:
   ```
   sudo nano .env
   ```

4. Start the service:
   ```
   sudo systemctl start server-agent
   ```

### Manual Installation

If you prefer to install manually:

1. Clone this repository:
   ```
   git clone https://github.com/yourusername/server-agent.git
   cd server-agent
   ```

2. Install required packages:
   ```
   sudo pip3 install -r requirements.txt
   ```

3. Create and configure the `.env` file:
   ```
   cp .env.example .env
   nano .env
   ```

4. Make the main script executable:
   ```
   chmod +x main.py
   ```

5. Install the systemd service:
   ```
   sudo cp server-agent.service /etc/systemd/system/
   sudo systemctl daemon-reload
   sudo systemctl enable server-agent
   sudo systemctl start server-agent
   ```

## Usage

### Managing the Service

If you installed the agent as a systemd service, you can manage it with the following commands:

```
# Start the service
sudo systemctl start server-agent

# Stop the service
sudo systemctl stop server-agent

# Restart the service
sudo systemctl restart server-agent

# Check the status
sudo systemctl status server-agent

# View logs
sudo journalctl -u server-agent -f
```

### Running Manually

For testing or debugging, you can run the agent manually:

```
# Run in the foreground with debug output
python3 main.py --no-daemon --debug

# Run only specific components
python3 main.py --monitor-only --no-daemon
python3 main.py --log-only --no-daemon
python3 main.py --heal-only --no-daemon
```

### Command-Line Options

The agent supports the following command-line options:

- `--monitor-only`: Run only the server monitoring component
- `--log-only`: Run only the log checking component
- `--heal-only`: Run only the auto-healing component
- `--no-daemon`: Run in foreground (don't daemonize)
- `--debug`: Enable debug logging

## Configuration Options

The agent can be configured through the `.env` file:

### Monitoring Settings
- `MONITOR_INTERVAL`: How often to check server resources (in seconds)
- `CPU_THRESHOLD`: CPU usage percentage threshold for alerts
- `MEMORY_THRESHOLD`: Memory usage percentage threshold for alerts
- `DISK_THRESHOLD`: Disk usage percentage threshold for alerts
- `ALERT_COOLDOWN`: Time to wait before sending another alert for the same issue (in seconds)

### Log Checking Settings
- `LOG_CHECK_INTERVAL`: How often to check logs (in seconds)
- `LOG_ALERT_COOLDOWN`: Time to wait before sending another alert for the same log issue (in seconds)

### Auto-Healing Settings
- `AUTO_HEAL_ENABLED`: Enable/disable automatic healing actions
- `HEAL_CHECK_INTERVAL`: How often to run healing checks (in seconds)
- `HEAL_ACTION_COOLDOWN`: Time to wait before attempting the same healing action again (in seconds)
- `MONITORED_SERVICES`: Comma-separated list of services to monitor and auto-heal

## Logs

The agent logs its activities to:

1. **Log file**: `server_agent.log` in the agent directory (or `/var/log/server-agent/server_agent.log` if installed with the installation script)
2. **System journal**: When running as a systemd service, logs are also sent to the system journal

You can view the logs using:

```
# View the log file
tail -f /var/log/server-agent/server_agent.log

# View logs in the system journal
sudo journalctl -u server-agent -f
```

### Troubleshooting

If the agent isn't working as expected:

1. Run with debug logging:
   ```
   python3 main.py --no-daemon --debug
   ```

2. Check for errors in the log file:
   ```
   grep -i error server_agent.log
   ```

3. Verify the .env configuration:
   ```
   cat .env
   ```

4. Check if required directories exist:
   ```
   ls -la agent/
   ls -la utils/
   ```

## License

MIT

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.
